const UPDATE_COMPLETE_CLUB = `
mutation updateUserClub($user_id: uuid!, $club_id: Int!, $user_club: user_club_set_input, $global_club: courses_set_input) {
    update_user_club(where: {club_id: {_eq: $club_id}, user_id: {_eq: $user_id}}, _set: $user_club) {
      affected_rows
      returning {
        proximity
        paymentMethod
        otherInstructions
      }
    }
    update_courses(where: {id: {_eq: $club_id}}, _set: $global_club) {
      affected_rows
      returning {
        id
        name
        hasGuestRestrictions
        guest_time_restrictions
        guestFee
        dressCode
        caddieRequired
        caddie<PERSON>ee
        new
      }
    }
  }  
`;

const DELETE_USER_CLUB = `
mutation deleteUserClub ($user_id: uuid!, $club_id: Int!) {
  delete_user_club_by_pk(club_id: $club_id, user_id: $user_id) {
    club_id
  }
}
`;

const UPDATE_USER_CLUB = `
mutation updateUserClub($user_id: uuid!, $club_id: Int!, $user_club: user_club_set_input!) {
  update_user_club_by_pk(pk_columns: {club_id: $club_id, user_id: $user_id}, _set: $user_club) {
    club_id
  }
}
`;

const FAVORITE_CLUB = `
mutation favoriteClub($user_id: uuid!, $club_id: Int!) {
  insert_favorite_club_one(object: {club_id: $club_id, user_id: $user_id}) {
    club_id
  }
}
`;

const UNFAVORITE_CLUB = `
mutation unfavoriteClub($user_id: uuid!, $club_id: Int!) {
  delete_favorite_club_by_pk(club_id: $club_id, user_id: $user_id) {
    club_id
  }
}
`;

const UPDATE_USER_YEARLY_CLUB_VALIDATION = `
    mutation UpdateUserYearlyClubValidation($user_id: uuid!, $yearly_club_validation: Int!) {
        update_user_by_pk(pk_columns: { id: $user_id }, _set: { yearly_club_validation: $yearly_club_validation }) {
            id
            yearly_club_validation
        }
    }
`

const UPDATE_USER_CLUB_REVIEW_DISABLED = `
    mutation updateUserClubReviewDisabled($user_id: uuid!, $club_id: Int!, $review_disabled: Boolean!) {
        update_user_club(where: {user_id: {_eq: $user_id}, club_id: {_eq: $club_id}}, _set: {review_disabled: $review_disabled}) {
            affected_rows
            returning {
                review_disabled
            }
        }
    }
`



export {
    UPDATE_COMPLETE_CLUB,
    DELETE_USER_CLUB,
    UPDATE_USER_CLUB,
    FAVORITE_CLUB,
    UNFAVORITE_CLUB,
    UPDATE_USER_YEARLY_CLUB_VALIDATION,
    UPDATE_USER_CLUB_REVIEW_DISABLED
};
