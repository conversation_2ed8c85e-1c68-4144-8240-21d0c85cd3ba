import React, { useRef, useEffect, useContext, useState } from 'react';
import { View, Text, TouchableOpacity, KeyboardAvoidingView, Pressable, ActivityIndicator } from 'react-native';
import { AuthContext } from '../../context/AuthContext';
import Form, { FormContext } from '../../forms/FormContext';
import { colors } from '../../theme/theme';
import Checkbox from '../fields/Checkbox';
import TGCustomModalView from './TGCustomModalView';
import CloseIcon from '../../assets/images/close-black.svg';
import OTPTextInput from 'react-native-otp-textinput';
import PhoneNumberVerifiedIcon from '../../assets/images/number-verified.svg';
import BackgroundTimer from 'react-native-background-timer';
import TGText from '../fields/TGText';
import constants from '../../utils/constants/constants';
import { Spacing, Typography } from '../../utils/responsiveUI';
const CleverTap = require('clevertap-react-native');

export default function PasswordModal({
    closeModal,
    onValueChanged,
    resendCode,
    phoneNumber,
    isVerified,
    errorsState,
    loading = false,
    type = '',
    header = '',
    subHeader = '',
    isAdminLogin = false,
}) {
    const { form, updateForm } = useContext(FormContext);
    const [counter, setCounter] = useState(120);
    const [errors, setErrors] = errorsState;

    useEffect(() => {
        if (counter > 0)
            BackgroundTimer.runBackgroundTimer(() => {
                //code that will be called every second
                setCounter(counter - 1);
            }, 1000);

        if (counter <= 0) BackgroundTimer.stopBackgroundTimer();
        return () => BackgroundTimer.stopBackgroundTimer();
    }, [counter]);

    function resendVerificationCode(params) {
        CleverTap.recordEvent(constants.CLEVERTAP.PROFILE.RESENT_VERIFICATION_CODE);
        setCounter(120);
        resendCode(form);
    }

    const timer = `${parseInt(counter / 60) < 10 ? '0' + parseInt(counter / 60) : parseInt(counter / 60)}:${
        counter % 60 < 10 ? '0' + (counter % 60) : counter % 60
    }`;

    return (
        <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.70)' }}>
            <Pressable style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0)' }}>
                <View style={{ flex: 1 }}></View>
            </Pressable>

            <KeyboardAvoidingView
                behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 200,
                    justifyContent: 'flex-end',
                }}>
                <View
                    style={{
                        borderTopLeftRadius: 10,
                        borderTopRightRadius: 10,
                        backgroundColor: 'white',
                        justifyContent: 'center',
                    }}>
                    <View>
                        {!isVerified && type === '' ? (
                            <Pressable onPress={closeModal}>
                                <CloseIcon height={15} width={15} style={{ alignSelf: 'flex-end', margin: 20 }} />
                            </Pressable>
                        ) : (
                            <PhoneNumberVerifiedIcon style={{ alignSelf: 'center', marginTop: 60, marginBottom: 20 }} />
                        )}
                        <Text
                            style={{
                                fontSize: 24,
                                fontFamily: 'Ubuntu-Medium',
                                textAlign: 'center',
                                color: colors.lightBlack,
                                paddingBottom: 8,
                                lineHeight: 28,
                            }}>
                            {header
                                ? header
                                : !isVerified
                                ? 'Verify Phone Number'
                                : 'Phone Number\nSuccessfully Verified'}
                        </Text>
                        <View />

                        <View style={{ justifyContent: 'center', paddingHorizontal: 20 }}>
                            {!isVerified && (
                                <Text
                                    style={{
                                        fontSize: 14,
                                        fontFamily: 'Ubuntu-Regular',
                                        textAlign: 'center',
                                        color: colors.lightBlack,
                                    }}>
                                    {subHeader ? subHeader : 'Please enter the verfication code sent on your email'}
                                </Text>
                            )}

                            <Text
                                style={{
                                    fontSize: 14,
                                    fontFamily: 'Ubuntu-Regular',
                                    textAlign: 'center',
                                    color: colors.lightBlack,
                                    marginTop: 5,
                                    marginBottom: 40,
                                }}>
                                {!isVerified
                                    ? phoneNumber
                                    : 'Please make note of login credentials and continue ahead to complete your registration'}
                            </Text>

                            {!isVerified && (
                                <OTPTextInput
                                    inputCount={6}
                                    inputCellLength={1}
                                    textInputStyle={{
                                        width: 45,
                                        height: 45,
                                        backgroundColor: colors.lightgray,
                                        color: colors.lightBlack,
                                        fontSize: 14,
                                        fontFamily: 'Ubuntu-Regular',
                                    }}
                                    offTintColor="transparent"
                                    tintColor="transparent"
                                    handleTextChange={(code) => {
                                        setErrors({ code: null });
                                        updateForm('code', code);
                                    }}
                                />
                            )}

                            {errors && errors['code'] && !isVerified && (
                                <Text
                                    style={{
                                        color: 'red',
                                        fontFamily: 'Ubuntu-Light',
                                        textAlign: 'center',
                                        fontSize: 12,
                                        marginTop: 8,
                                    }}>
                                    {errors['code']}
                                </Text>
                            )}

                            {(!isVerified || isAdminLogin) && (
                                <TouchableOpacity
                                    style={{ alignItems: 'center', marginTop: 50 }}
                                    onPress={() => counter <= 0 && resendVerificationCode()}>
                                    {counter > 0 && (
                                        <TGText style={{ fontSize: 18, color: colors.darkteal, marginBottom: 5 }}>
                                            {timer}
                                        </TGText>
                                    )}
                                    <Text
                                        style={{
                                            color: colors.lightBlack,
                                            textDecorationLine: 'underline',
                                            fontFamily: 'Ubuntu-Regular',
                                            opacity: counter > 0 ? 0.3 : 1,
                                        }}>
                                        Resend Verification Code
                                    </Text>
                                </TouchableOpacity>
                            )}
                        </View>

                        {isAdminLogin && (
                            <Checkbox
                                textStyle={{ fontSize: 12, fontFamily: 'Ubuntu-Regular', color: colors.lightBlack }}
                                checkStyle={{ height: 22, width: 22, borderRadius: 2 }}
                                containerStyle={{
                                    paddingHorizontal: Spacing.SCALE_16,
                                    marginTop: Spacing.SCALE_10,
                                    alignItems: 'center',
                                }}
                                type={'createaccount'}
                                isAdminLogin={true}
                                name="RememberMe"
                                label="Remember Me"
                                showpolicy={true}
                                child={
                                    <Text
                                        style={[
                                            {
                                                paddingLeft: 10,
                                                fontFamily: 'Ubuntu-Regular',
                                                color: colors.lightBlack,
                                                fontSize: Typography.FONT_SIZE_12,
                                                lineHeight: 16,
                                            },
                                        ]}>
                                        Remember Me
                                    </Text>
                                }
                            />
                        )}

                        <View style={{ justifyContent: 'center', margin: 20, paddingLeft: 10, paddingBottom: 30 }}>
                            {loading ? (
                                <ActivityIndicator color={colors.darkteal} />
                            ) : (
                                <TouchableOpacity
                                    style={{
                                        width: '100%',
                                        borderRadius: 4,
                                        backgroundColor: colors.darkteal,
                                        marginRight: 10,
                                        height: 50,
                                        justifyContent: 'center',
                                    }}
                                    onPress={() => onValueChanged(form)}>
                                    <View>
                                        <Text
                                            style={{
                                                fontSize: 16,
                                                fontFamily: 'Ubuntu-Medium',
                                                textAlign: 'center',
                                                color: 'white',
                                            }}>
                                            {!isVerified ? 'Verify' : 'Continue'}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            )}
                        </View>

                        {type !== '' && (
                            <TGText
                                style={{
                                    color: colors.darkteal,
                                    fontFamily: 'Ubuntu-Regular',
                                    marginBottom: 50,
                                    textAlign: 'center',
                                    marginHorizontal: 30,
                                    marginTop: -30,
                                    fontSize: 12,
                                }}>
                                We were unable to verify your phone number earlier, please re-enter and verify the same
                            </TGText>
                        )}
                    </View>
                </View>
            </KeyboardAvoidingView>
        </View>
    );
}
