import React, { useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    Image,
    StyleSheet,
    Alert,
    ScrollView,
    ActivityIndicator,
} from 'react-native';

import {
    pickImageFromGalleryWithCrop,
    launchCameraWithCrop,
    compressImage,
    pickAndCompressImageFromGallery,
    launchCameraAndCompressImage,
} from '../../utils/commonActions';

interface ImagePickerDemoProps {
    onImageSelected?: (imageUri: string) => void;
}

const ImagePickerDemo: React.FC<ImagePickerDemoProps> = ({ onImageSelected }) => {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [compressedImage, setCompressedImage] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [imageInfo, setImageInfo] = useState<any>(null);

    const handleImageSelection = (imageUri: string, imageData?: any) => {
        setSelectedImage(imageUri);
        if (imageData) {
            setImageInfo(imageData);
        }
        onImageSelected?.(imageUri);
    };

    const pickFromGallery = async () => {
        try {
            setLoading(true);
            const image = await pickImageFromGalleryWithCrop({
                width: 800,
                height: 800,
                cropping: true,
                cropperCircleOverlay: false,
                compressImageQuality: 0.8,
            });
            
            handleImageSelection(image.path, image);
            Alert.alert('Success', 'Image selected from gallery!');
        } catch (error: any) {
            console.error('Gallery picker error:', error);
            if (error.message !== 'User cancelled image selection') {
                Alert.alert('Error', 'Failed to pick image from gallery');
            }
        } finally {
            setLoading(false);
        }
    };

    const launchCamera = async () => {
        try {
            setLoading(true);
            const image = await launchCameraWithCrop({
                width: 1024,
                height: 1024,
                cropping: true,
                cropperCircleOverlay: false,
                compressImageQuality: 0.9,
            });
            
            handleImageSelection(image.path, image);
            Alert.alert('Success', 'Image captured from camera!');
        } catch (error: any) {
            console.error('Camera launcher error:', error);
            if (error.message !== 'User cancelled image selection') {
                Alert.alert('Error', 'Failed to capture image from camera');
            }
        } finally {
            setLoading(false);
        }
    };

    const compressCurrentImage = async () => {
        if (!selectedImage) {
            Alert.alert('Error', 'Please select an image first');
            return;
        }

        try {
            setLoading(true);
            const compressed = await compressImage(selectedImage, {
                compressionMethod: 'manual',
                maxWidth: 800,
                quality: 0.6,
            });
            
            setCompressedImage(compressed);
            Alert.alert('Success', 'Image compressed successfully!');
        } catch (error) {
            console.error('Compression error:', error);
            Alert.alert('Error', 'Failed to compress image');
        } finally {
            setLoading(false);
        }
    };

    const pickAndCompress = async () => {
        try {
            setLoading(true);
            const result = await pickAndCompressImageFromGallery(
                {
                    width: 800,
                    height: 800,
                    cropping: true,
                    compressImageQuality: 0.8,
                },
                {
                    compressionMethod: 'manual',
                    maxWidth: 1280,
                    quality: 0.7,
                }
            );
            
            handleImageSelection(result.originalImage.path, result.originalImage);
            setCompressedImage(result.compressedUri);
            Alert.alert('Success', 'Image picked and compressed!');
        } catch (error: any) {
            console.error('Pick and compress error:', error);
            if (error.message !== 'User cancelled image selection') {
                Alert.alert('Error', 'Failed to pick and compress image');
            }
        } finally {
            setLoading(false);
        }
    };

    const cameraAndCompress = async () => {
        try {
            setLoading(true);
            const result = await launchCameraAndCompressImage(
                {
                    width: 1024,
                    height: 1024,
                    cropping: true,
                    compressImageQuality: 0.9,
                },
                {
                    compressionMethod: 'manual',
                    maxWidth: 1280,
                    quality: 0.8,
                }
            );
            
            handleImageSelection(result.originalImage.path, result.originalImage);
            setCompressedImage(result.compressedUri);
            Alert.alert('Success', 'Image captured and compressed!');
        } catch (error: any) {
            console.error('Camera and compress error:', error);
            if (error.message !== 'User cancelled image selection') {
                Alert.alert('Error', 'Failed to capture and compress image');
            }
        } finally {
            setLoading(false);
        }
    };

    const profilePhotoPicker = async () => {
        try {
            setLoading(true);
            const result = await pickAndCompressImageFromGallery(
                {
                    width: 400,
                    height: 400,
                    cropping: true,
                    cropperCircleOverlay: true, // Circular crop
                    cropperToolbarTitle: 'Crop Profile Photo',
                    compressImageQuality: 0.8,
                },
                {
                    compressionMethod: 'manual',
                    maxWidth: 800,
                    quality: 0.8,
                }
            );
            
            handleImageSelection(result.originalImage.path, result.originalImage);
            setCompressedImage(result.compressedUri);
            Alert.alert('Success', 'Profile photo selected!');
        } catch (error: any) {
            console.error('Profile photo picker error:', error);
            if (error.message !== 'User cancelled image selection') {
                Alert.alert('Error', 'Failed to pick profile photo');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>Image Picker Demo</Text>
            
            {loading && (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.loadingText}>Processing...</Text>
                </View>
            )}

            <View style={styles.buttonContainer}>
                <TouchableOpacity style={styles.button} onPress={pickFromGallery}>
                    <Text style={styles.buttonText}>Pick from Gallery</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.button} onPress={launchCamera}>
                    <Text style={styles.buttonText}>Launch Camera</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.button} onPress={compressCurrentImage}>
                    <Text style={styles.buttonText}>Compress Current Image</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.button} onPress={pickAndCompress}>
                    <Text style={styles.buttonText}>Pick & Compress</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.button} onPress={cameraAndCompress}>
                    <Text style={styles.buttonText}>Camera & Compress</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.profileButton} onPress={profilePhotoPicker}>
                    <Text style={styles.buttonText}>Profile Photo (Circular)</Text>
                </TouchableOpacity>
            </View>

            {selectedImage && (
                <View style={styles.imageContainer}>
                    <Text style={styles.imageTitle}>Original Image:</Text>
                    <Image source={{ uri: selectedImage }} style={styles.image} />
                    {imageInfo && (
                        <Text style={styles.imageInfo}>
                            Size: {Math.round(imageInfo.size / 1024)}KB | 
                            Dimensions: {imageInfo.width}x{imageInfo.height}
                        </Text>
                    )}
                </View>
            )}

            {compressedImage && (
                <View style={styles.imageContainer}>
                    <Text style={styles.imageTitle}>Compressed Image:</Text>
                    <Image source={{ uri: compressedImage }} style={styles.image} />
                </View>
            )}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: '#f5f5f5',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#333',
    },
    loadingContainer: {
        alignItems: 'center',
        marginVertical: 20,
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#666',
    },
    buttonContainer: {
        marginBottom: 20,
    },
    button: {
        backgroundColor: '#007AFF',
        padding: 15,
        borderRadius: 8,
        marginBottom: 10,
        alignItems: 'center',
    },
    profileButton: {
        backgroundColor: '#34C759',
        padding: 15,
        borderRadius: 8,
        marginBottom: 10,
        alignItems: 'center',
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    imageContainer: {
        marginBottom: 20,
        alignItems: 'center',
    },
    imageTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 10,
        color: '#333',
    },
    image: {
        width: 200,
        height: 200,
        borderRadius: 10,
        backgroundColor: '#ddd',
    },
    imageInfo: {
        marginTop: 10,
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
    },
});

export default ImagePickerDemo;
