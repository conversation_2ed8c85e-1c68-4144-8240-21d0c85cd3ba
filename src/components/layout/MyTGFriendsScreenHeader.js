import React, { useContext } from 'react';
import {
    Text,
    View,
    TouchableOpacity,
    StatusBar,
    StyleSheet,
    Platform,
} from 'react-native';
import SearchIcon from '../../assets/svg/updatedSearchIcon.svg';
import MyFriendMapIcon from '../../assets/svg/MyFriendMapIcon.svg';
import FilterIcon from '../../assets/images/svg/icon-filter.svg';
import { Back } from '../../assets/images/svg';
import TrippleDot from '../../assets/svg/TrippleDot.svg';
import CreatePostIcon from '../../assets/svg/AddPostIcon.svg';
import { colors } from '../../theme/theme';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { useNavigation } from '@react-navigation/native';
import config from '../../config';
import { GlobalContext } from '../../context/contextApi';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ALL } from '../../utils/constants/strings';

function MyTGFriendsScreenHeader({
    title,
    filterState = [null, () => {}],
    showFilter = false,
    openFilterModal,
    popupState,
    showMapIcon,
    showSearchIcon = false,
    setOpenSearchBar = () => {},
    onTripleDotPress = () => {},
}) {
    const insets = useSafeAreaInsets();
    const [addFriendModal, setAddFriendModal] = popupState;
    const navigation = useNavigation();

    const [filter, setFilter] = filterState;
    const { actions } = useContext(GlobalContext);

    return (
        <>
            <StatusBar barStyle="dark-content" backgroundColor={colors.whiteRGB} />
            <View style={[
                        styles.safeAreaStyle,
                        { paddingTop: Platform.OS === 'ios' ? Spacing.SCALE_40 : insets.top + Spacing.SCALE_20 },
                    ]}>
                <View style={styles.container}>
                    <TouchableOpacity
                        onPress={() => {
                            navigation.goBack();
                        }}
                        style={styles.drawerIconWrapper}>
                        <Back height={25} />
                    </TouchableOpacity>
                    <Text style={styles.headerWrapper}>{title}</Text>
                    <View style={styles.box}>
                        {showFilter && (
                            <TouchableOpacity
                                onPress={() => {
                                    openFilterModal();
                                }}
                                style={{ marginRight: 10 }}>
                                <FilterIcon
                                    fill={filter ? colors.darkteal : colors.darkCharcoal}
                                    width={20}
                                    height={20}
                                />
                            </TouchableOpacity>
                        )}

                        {/* Search Icon */}
                        {showSearchIcon && (
                            <TouchableOpacity
                                onPress={() => {
                                    setOpenSearchBar((prev) => !prev);
                                }}
                                style={styles.iconWrapper}>
                                <SearchIcon width={20} height={20} />
                            </TouchableOpacity>
                        )}

                        {/** Add Friend Icon */}
                        <TouchableOpacity style={styles.iconWrapper} onPress={() => setAddFriendModal(true)}>
                            <CreatePostIcon />
                        </TouchableOpacity>

                        {/* Show map option in all friend header */}
                        {showMapIcon && (
                            <TouchableOpacity
                                onPress={() => {
                                    // set Map filter in map screen after redirection to update filter data
                                    actions.setMapFilterState({
                                        friendsAndContact: true,
                                        clubMemberCount: ALL,
                                        clubPercentage: ALL,
                                    });
                                    // Navigation method to navigate on map screen
                                    navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                                        screen: config.routes.CLUBS,
                                        params: { category: 'Friends' },
                                    });
                                }}
                                style={styles.iconWrapper}>
                                <MyFriendMapIcon height={19} width={19} />
                            </TouchableOpacity>
                        )}

                        {/* Map icon */}
                        <TouchableOpacity style={[styles.iconWrapper, { marginRight: 0 }]} onPress={onTripleDotPress}>
                            <TrippleDot height={18} width={18} />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </>
    );
}

export default MyTGFriendsScreenHeader;

const styles = StyleSheet.create({
    safeAreaStyle: {
        width: '100%',
        zIndex: 100,
        backgroundColor: 'rgba(255, 255, 255, 1)',
    },
    container: {
        width: '100%',
        alignItems: 'center',
        height: 60,
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_15,
    },
    drawerIconWrapper: {
        zIndex: 100,
        height: Size.SIZE_34,
        alignItems: 'flex-start',
        justifyContent: 'center',
        width: Size.SIZE_34,
    },
    headerWrapper: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_18,
        left: Spacing.SCALE_50,
        right: 0,
        position: 'absolute',
        top: Platform.OS === 'ios' ? Spacing.SCALE_15 : Spacing.SCALE_15,
        fontWeight: '500',
        color: colors.lightBlack,
        textAlign: 'auto',
    },
    box: {
        position: 'absolute',
        zIndex: 50,
        flexDirection: 'row',
        right: 15,
        top: Spacing.SCALE_10,
        alignItems: 'center',
        flex: 1,
        width: 'auto',
        justifyContent: 'space-around',
    },
    iconWrapper: {
        marginRight: Spacing.SCALE_12,
        width: Size.SIZE_30,
        height: Size.SIZE_30,
        borderRadius: Size.SIZE_8,
        borderColor: colors.darkgray,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
    },
});
