import { ImageBackground, Platform, StatusBar, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// interface, theme, utils imports
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { HomeScreenCommonHeaderProps } from '../../interface';
import { TgWhiteLogo } from '../../assets/svg';

const AdminHeader: React.FC<HomeScreenCommonHeaderProps> = ({ title }) => {
    const insets = useSafeAreaInsets();

    return (
        <>
            <StatusBar barStyle="dark-content" backgroundColor={colors.tealRgb} />
            <ImageBackground
                source={require('../../assets/images/profileBG.png')}
                style={{
                    height: Platform.OS === 'ios' ? Size.SIZE_200 : Size.SIZE_150,
                    width: '100%',
                    paddingTop: Platform.OS === 'ios' ? Spacing.SCALE_60 : insets.top + Spacing.SCALE_20,
                }}
                imageStyle={{
                    borderBottomLeftRadius: Spacing.SCALE_16,
                    borderBottomRightRadius: Spacing.SCALE_16,
                }}>
                <View style={[styles.container]}>
                    <TgWhiteLogo />
                    <Text style={styles.headerTitle}>Login as User</Text>
                    <Text style={styles.headerSubTitle}>Enter a user’s email to log in as that user.</Text>
                </View>
            </ImageBackground>
        </>
    );
};

export default AdminHeader;

const styles = StyleSheet.create({
    container: {
        width: '100%',
        zIndex: 100,
        paddingHorizontal: Spacing.SCALE_16,
    },
    headerTitle: {
        color: colors.whiteRGB,
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginTop: Spacing.SCALE_30,
    },
    headerSubTitle: {
        color: colors.lightgray,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_8,
    },
});
