import React from 'react';
import { TouchableOpacity, StyleSheet, ActivityIndicator, Text } from 'react-native';

//utils and theme imports
import { colors } from '../../theme/theme';
import { Typography } from '../../utils/responsiveUI';

export default function TealButtonNew({
    text = '',
    onPress = () => {},
    btnStyle = {},
    textStyle = {},
    loading = false,
    disabled = false,
    disabledStyle = null,
}) {
    return (
        <TouchableOpacity
            style={[styles.buttonSave, btnStyle, disabledStyle ? styles.disabledBtn : {}]}
            onPress={loading ? () => {} : onPress}
            disabled={disabled}>
            {loading ? (
                <ActivityIndicator color="white" />
            ) : (
                <Text style={[disabledStyle ? styles.disabledBtnText : styles.btnTextStyle, textStyle]}>{text}</Text>
            )}
        </TouchableOpacity>
    );
}
const styles = StyleSheet.create({
    buttonSave: {
        backgroundColor: colors.darkteal,
        width: '50%',
        paddingVertical: 15,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
    btnTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        color: colors.white,
    },
    disabledBtnText: {
        color: '#666',
    },
    disabledBtn: {
        backgroundColor: 'rgba(196, 196, 196, 0.4)',
    },
});
