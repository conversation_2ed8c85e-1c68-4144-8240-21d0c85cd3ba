import React, { useContext, useState } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    ActivityIndicator,
    StyleSheet,
    Alert,
} from 'react-native';
import { Image as ImageCompressor } from 'react-native-compressor';
import firebase from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/storage';

import useClient from '../../hooks/useClient';
import { AuthContext } from '../../context/AuthContext';
import { UPDATE_USER } from '../../graphql/mutations/user';
import { DefaultNameProfile } from '../../utils/helpers/PersonalProfileHelper';
import EditProfileSvg from '../../assets/images/EditProfileSvg.svg';
import { Size, Typography } from '../../utils/responsiveUI';
import useThumbnail from '../../hooks/useThumbnail';
import constants from '../../utils/constants/constants';
import { pickImageFromGalleryWithCrop } from '../../utils/commonActions';

async function uploadImage(file) {
    try {
        // Generate a unique filename
        const timestamp = Date.now();
        const fileName = `profile_${timestamp}.jpg`;

        const storageRef = firebase
            .storage()
            .ref()
            .child(`profilephotos/${firebase.auth()?.currentUser?.uid}-${fileName}`);

        // Use putFile for local file URIs
        const uploadTask = await storageRef.putFile(file.path);

        const downloadURL = await storageRef.getDownloadURL();

        return downloadURL;
    } catch (error) {
        console.error('Upload error:', error);
        throw error;
    }
}

export default function EditProfilePhoto(props) {
    const [photo, setPhoto] = useState(props.photo);
    const [loading, setLoading] = useState(false);
    const { refreshUser, user } = useContext(AuthContext);
    const client = useClient();
    const { thumbnailUrl } = useThumbnail(user.profilePhoto, constants.ImageSize[128]);

    async function uploadPhoto(file) {
        setLoading(true);

        try {
            // Compress the image size before uploading
            const compressedUrl = await ImageCompressor.compress(file.path, {
                compressionMethod: 'manual',
                maxWidth: 1280,
                quality: 0.8,
            });
            // Create a new file object with compressed URI
            const compressedFile = {
                ...file,
                path: compressedUrl,
            };

            const photoURL = await uploadImage(compressedFile);

            await client.request(UPDATE_USER, {
                user_id: firebase.auth()?.currentUser?.uid,
                user: {
                    profilePhoto: photoURL,
                },
            });

            await refreshUser();
            setLoading(false);
        } catch (error) {
            Alert.alert('Error', 'Failed to upload image. Please try again.');
            setLoading(false);
        }
    }

    const showImagePicker = async () => {
        try {
            // Use react-native-image-crop-picker
            const image = await pickImageFromGalleryWithCrop({
                width: 400,
                height: 400,
                cropping: true,
                cropperCircleOverlay: false,
                freeStyleCropEnabled: true,
                cropperToolbarTitle: 'Crop Profile Photo',
                cropperCancelText: 'Cancel',
                cropperChooseText: 'Choose',
                mediaType: 'photo',
            });

            if (image) {

                // Check if we have the required properties
                if (!image.path) {
                    return;
                }

                // Check file size (1.5MB limit)
                // if (image.size && image.size > 1500000) {
                //     Alert.alert('', 'Please upload the file as per required size and format');
                //     return;
                // }

                setPhoto(image.path);
                uploadPhoto(image);
            } else {
                console.log('No image selected');
            }
        } catch (error) {
            console.error('Image picker error:', error);

            if (error.code === 'E_PICKER_CANCELLED') {
                console.log('User cancelled image picker');
                // Don't show alert for cancellation
            } else {
                Alert.alert('Error', `Failed to pick image: ${error.message || 'Unknown error'}`);
            }
        }
    };

    return (
        <View>
            {thumbnailUrl ? (
                <Image source={{ uri: thumbnailUrl }} style={styles.profileContainerStyle} />
            ) : (
                <DefaultNameProfile
                    fName={props?.user?.first_name}
                    lName={props?.user?.last_name}
                    containerStyle={styles.profileContainerStyle}
                    textStyle={styles.initialsNameStyle}
                    screenName={'Profile'}
                />
            )}
            <TouchableOpacity onPress={showImagePicker} style={styles.editBtnStyle}>
                <EditProfileSvg height={15} width={15} />
            </TouchableOpacity>
            {loading && (
                <View style={styles.loaderStyle}>
                    <ActivityIndicator color="white" />
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    profileContainerStyle: {
        height: Size.SIZE_90,
        width: Size.SIZE_90,
        borderRadius: Size.SIZE_12,
    },
    initialsNameStyle: {
        fontSize: Typography.FONT_SIZE_30,
        textTransform: 'uppercase',
    },
    editBtnStyle: {
        backgroundColor: 'white',
        position: 'absolute',
        zIndex: 110,
        right: -10,
        top: -10,
        width: 30,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        borderRadius: 50,
        elevation: 5,
    },
    loaderStyle: {
        borderRadius: 10,
        backgroundColor: 'rgba(0,0,0,0.5)',
        zIndex: 50,
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
