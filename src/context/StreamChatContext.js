import React, { createContext, useEffect, useState } from 'react';
import { StreamChat } from 'stream-chat';
import auth from '@react-native-firebase/auth';
import messaging from '@react-native-firebase/messaging';
import firebase from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/storage';
import Config from 'react-native-config';

import { GET_STREAM_CHAT_TOKEN } from '../service/EndPoint';
import { imgFile } from '../screens/my-TG-Stream-Chat/constants';
import config from '../config';
import {
    ADMIN_CREATED_GROUP,
    MY_TG_GROUP,
    ONE_TO_ONE,
    SYSTEM_THOUSAND_GREENS_PUBLIC,
    USER_CREATED_GROUP,
} from '../screens/my-TG-Stream-Chat/client';

export const StreamChatContext = createContext();

export default function StreamChatProvider({ user, children, client, shareSheetImageUrl, navigationRef }) {
    const [streamToken, setStreamToken] = useState();
    const [unreadcount, setUnreadcount] = useState(0);
    const [channel, setChannel] = useState({});
    const [channelsBlockState, setChannelsBlockState] = useState({});
    const [mutedChannels, setMutedChannels] = useState([]);
    const [shareImageObject, setShareImageObject] = useState([]);
    const [shareSheetCount, setShareSheetCount] = useState(0);
    const [previousChannel, setPreviousChannel] = useState({});
    const [localMessage, setLocalMessage] = useState({});

    // This useEffect call when we have shareSheet image local path and navigate to TG chat screen and call handleGetImageFile function
    useEffect(() => {
        if (shareSheetImageUrl?.length) {
            handleGetImageFile();
            navigationRef.current.navigate(config.routes.SHARE_SHEET_SCREEN);
        }
    }, [JSON.stringify(shareSheetImageUrl)]);

    // This function is used to convert local path in to image files
    const handleGetImageFile = () => {
        let tempImgObjectArr = [];
        let fp = 'file://';
        shareSheetImageUrl?.map((imgLocalPath) => {
            tempImgObjectArr.push({ ...imgFile, path: fp + imgLocalPath });
        });
        setShareImageObject([...tempImgObjectArr]);
    };

    const getStreamToken = async () => {
        const token = await auth()?.currentUser?.getIdToken();
        const streamToken = await fetch(GET_STREAM_CHAT_TOKEN, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                userId: user?.id,
            }),
        })
            .then(async (data) => data.json())
            .then((res) => {
                setStreamToken(res.streamToken);
                return res.streamToken;
            })
            .catch((err) => {
                console.log('Error----->', err);
            });
        return streamToken;
    };

    const requestPermission = async () => {
        console.log('requestPermission call');
        const authStatus = await messaging().requestPermission();
    };

    const registerPushToken = async () => {
        // unsubscribe any previous listener
        try {
            const token = await messaging().getToken();

            const push_provider = 'firebase';
            const push_provider_name = Config.push_providerName; // name an alias for your push provider (optional)
            const response = await client.addDevice(token, push_provider, user?.id, push_provider_name);
            const id = await client.getDevices(user?.id);
        } catch (error) {
            console.log('error==>>', error);
        }
    };

    const getUnreadCount = async () => {
        try {
            let unreadChannels = await client.queryChannels({
                members: { $in: [user?.id] },
                has_unread: true,
                type: {
                    $in: [
                        USER_CREATED_GROUP,
                        SYSTEM_THOUSAND_GREENS_PUBLIC,
                        ADMIN_CREATED_GROUP,
                        MY_TG_GROUP,
                        ONE_TO_ONE,
                    ],
                },
            });
            setUnreadcount(unreadChannels?.length);
        } catch (error) {
            console.log('error-===>', error);
        }
    };

    const initChat = async () => {
        try {
            // Get the token first
            const token = await getStreamToken();
            // Then connect the user with the token
            const connection = await client.connectUser(
                {
                    id: user?.id,
                },
                token,
            );
            getUnreadCount();
        } catch (error) {
            console.log('error=>', error);
        }
    };

    useEffect(() => {
        if (user?.id) {
            initChat();
            requestPermission();
            registerPushToken();
        }

        return () => {
            client?.disconnectUser();
        };
    }, [user?.id]);

    const updateChannel = (channelInfo) => {
        setChannel(channelInfo);
    };

    const updateBlockedChannel = (channel) => {
        if (channel?.data?.frozen && channel?.data?.blockedBy?.length) {
            setChannelsBlockState({
                ...channelsBlockState,
                [channel.id]: {
                    blocked: true,
                    youHaveBlocked: channel?.data?.blockedBy.includes(user?.id),
                },
            });
        } else {
            setChannelsBlockState({
                ...channelsBlockState,
                [channel.id]: { blocked: false, youHaveBlocked: false },
            });
        }
    };

    const addMutedChannel = (channelId) => {
        if (!mutedChannels?.includes(channelId)) {
            setMutedChannels((prev) => [...prev, channelId]);
        }
    };

    const removeMutedChannel = (channelId) => {
        const remainingMutedChannels = [...mutedChannels];
        const index = remainingMutedChannels.indexOf(channelId);
        if (index > -1) {
            remainingMutedChannels.splice(index, 1);
        }
        setMutedChannels(remainingMutedChannels);
    };

    return (
        <StreamChatContext.Provider
            value={{
                client,
                chatClient: client,
                channel,
                setChannel,
                unreadcount,
                setUnreadcount,
                updateChannel,
                updateBlockedChannel,
                channelsBlockState,
                addMutedChannel,
                removeMutedChannel,
                mutedChannels,
                shareSheetImageUrl,
                shareImageObject,
                shareSheetCount,
                setShareSheetCount,
                setShareImageObject,
                previousChannel,
                setPreviousChannel,
                localMessage,
                setLocalMessage,
            }}>
            {children}
        </StreamChatContext.Provider>
    );
}
