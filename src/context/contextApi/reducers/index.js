export const defaultState = {
    selectedFriends: null,
    blockUserResponse: null,
    updatedChannel: null,
    channelMembers: null,
    userProfile: {},
    archiveState: false,
    updatedChannel: {},
    isMySelfRemove: false,
    selectedMessageId: null,
    otherUser: {},
    currentScreenName: null,
    clubDetails: {},
    selectedClub: {},
    allClubsState: [],
    boundariesChangeClub: {},
    allClubsData: [],
    myTgGroupDetails: {},
    myTgGroupMembers: {},
    refreshPage: false,
    allFriendsId: {},
    pendingJoinRequests: {},
    unreadChannels: [],
    requestCount: {},
    unReadMessageStatus: {},
    favClubs: [],
    currentTab: 3,
    mapFilterState: {
        category: 'All',
        clubPercentage: 'All',
        clubMemberCount: 'All',
        clubswithFemaleMembers: false,
        favoriteClubs: false,
        fern: false,
        friendsAndContact: false,
        moss: false,
        myTGGroupMember: false,
        olive: false,
        openOfferClubs: false,
        playAsCouple: false,
        playedClubs: false,
        sage: false,
        selectedTGGroup: [],
    },
    clubName: '',
    friendTab: 'All Friends',
    seeOfferClub: '',
    mapCurrentState: {},
    mapCurrentFilter: '',
    allFriendOpenSearchBar: false,
    allMemberScreenLoader: false,
    mapCurrentClub: null,
    pegboardDetails: {},
    comeFromNotification: null,
    zoomLevelState: 0,
    firstTimeInMap: true,
    myOwnClubs: null,
    selectedChannel: {},
    deletePopup: false,
    deletePopupLoader: false,
    country: [],
    state: [],
    city: [],
    isCrossIconToggle: false,
    poll: {},
    guideStep: 1,
    startGuide: false,
    notifications: [],
    stats: [
        { id: '1', value: 0, label: 'Members Added', name: 'new_active_members' },
        { id: '2', value: 0, label: 'Clubs Added', name: 'new_clubs' },
        { id: '3', value: 0, label: 'Requests Made', name: 'requests_accepted' },
        { id: '4', value: 0, label: 'Requests Accepted', name: 'requests_made' },
    ],
    mutePopup: false,
    isFirstTimeVisitHome: true,
    isMenuBottomSheetOpen: false,
    isMenuBottomSheetOpenIndex: 1,
    expanded: false,
    tealDotStatus: null,
    appLoader: false,
    appSkeltonLoader: false,
    selectedOffer: { isOpen: false, selectedOffer: null },
    offlineLogGameStep: 0,
    receivedOpen: [],
    requestedOpen: [],
    requestedAccepted: [],
    receivedAccepted: [],
    allAcceptedRequests: [],
    allRequestHostsData: [],
    unreadChannelsObject: [],
    requestUnreadChannels: [],
    isMapViewExpanded: false,
    screen: '',
    isMapSearchActive: false,
    mapBoundaries: null,
    gameReview: [],
    shouldDetailBottomSheetOpen: false,
    filterActive: false,
    homeScreenPopupSteps: 1,
    reactionState: {reactionListVisible: false, message: {}, ReactionType: '', allReactionsListVisible: false, reactions: []},
};
export const reducers = (prevState, action) => {
    switch (action.type) {
        case 'SELECTED_FRIENDS':
            return {
                ...prevState,
                selectedFriends: action?.data,
            };
        case 'BLOCKE_USER':
            return {
                ...prevState,
                blockUserResponse: action?.data,
            };
        case 'UPDATED_CHANNEL':
            return {
                ...prevState,
                updatedChannel: action?.data,
            };
        case 'CHANNEL_MEMBERS':
            return {
                ...prevState,
                channelMembers: action?.data,
            };
        case 'USER_PROFILE':
            return {
                ...prevState,
                userProfile: action?.data,
            };
        case 'SHOW_ARCHIVE_LIST':
            return {
                ...prevState,
                archiveState: action?.data,
            };
        case 'UPDATED_CHANNEL_ACTION':
            return {
                ...prevState,
                updatedChannel: action?.data,
            };
        case 'IS_MY_SELF_REMOVE':
            return {
                ...prevState,
                isMySelfRemove: action?.data,
            };
        case 'SELECTED_MESSAGE_ID':
            return {
                ...prevState,
                selectedMessageId: action?.data,
            };
        case 'SET_OTHER_USER':
            return {
                ...prevState,
                otherUser: action?.data,
            };
        case 'SET_CURRENT_SCREEN_NAME':
            return {
                ...prevState,
                currentScreenName: action?.data,
            };
        case 'SET_CLUB_DETAILS':
            return {
                ...prevState,
                clubDetails: action?.data,
            };
        case 'SET_SELECTED_CLUB':
            return {
                ...prevState,
                selectedClub: action?.data,
            };
        case 'SET_ALL_CLUBS':
            return {
                ...prevState,
                allClubsState: action?.data,
            };
        case 'SET_BOUNDARIES_CHANGE_CLUBS':
            return {
                ...prevState,
                boundariesChangeClub: action?.data,
            };
        case 'SET_ALL_CLUBS_DATA':
            return {
                ...prevState,
                allClubsData: action?.data,
            };
        case 'SET_MY_TG_GROUP_DETAILS':
            return {
                ...prevState,
                myTgGroupDetails: action?.data,
            };
        case 'SET_MY_TG_GROUP_MEMBERS':
            return {
                ...prevState,
                myTgGroupMembers: action?.data,
            };
        case 'SET_ALL_FRIENDS_ID':
            return {
                ...prevState,
                allFriendsId: action?.data,
            };
        case 'SET_REFRESH_PAGE':
            return {
                ...prevState,
                refreshPage: action?.data,
            };
        case 'SET_PENDING_JOIN_REQUESTS':
            return {
                ...prevState,
                pendingJoinRequests: action?.data,
            };
        case 'SET_UNREAD_CHANNELS':
            return {
                ...prevState,
                unreadChannels: action?.data,
            };
        case 'SET_REQUEST_COUNT':
            return {
                ...prevState,
                requestCount: action?.data,
            };
        case 'SET_UNREAD_MESSAGE_STATUS':
            return {
                ...prevState,
                unReadMessageStatus: action?.data,
            };
        case 'SET_FAV_CLUB':
            return {
                ...prevState,
                favClubs: action?.data,
            };
        case 'SET_CURRENT_TAB':
            return {
                ...prevState,
                currentTab: action?.data,
            };
        case 'SET_MAP_FILTER':
            return {
                ...prevState,
                mapFilterState: action?.data,
            };
        case 'SET_CLUB_NAME':
            return {
                ...prevState,
                clubName: action?.data,
            };
        case 'SET_FRIEND_TAB':
            return {
                ...prevState,
                friendTab: action?.data,
            };
        case 'SET_SEE_OFFER_CLUB':
            return {
                ...prevState,
                seeOfferClub: action?.data,
            };
        case 'SET_MAP_CURRENT_STATE':
            return {
                ...prevState,
                mapCurrentState: action?.data,
            };
        case 'SET_MAP_CURRENT_FILTER':
            return {
                ...prevState,
                mapCurrentFilter: action?.data,
            };
        case 'SET_OPEN_ALL_FRIEND_SEARCH':
            return {
                ...prevState,
                allFriendOpenSearchBar: action?.data,
            };
        case 'SET_ALL_MEMBER_SCREEN_LOADER':
            return {
                ...prevState,
                allMemberScreenLoader: action?.data,
            };
        case 'SET_MAP_CURRENT_CLUB':
            return {
                ...prevState,
                mapCurrentClub: action?.data,
            };
        case 'SET_COME_FROM_NOTIFICATION':
            return {
                ...prevState,
                comeFromNotification: action?.data,
            };
        case 'SET_PEGBOARD_DETAILS':
            return {
                ...prevState,
                pegboardDetails: action?.data,
            };
        case 'SET_ZOOM_LEVEL':
            return {
                ...prevState,
                zoomLevelState: action?.data,
            };
        case 'SET_FIRST_TIME_IN_MAP':
            return {
                ...prevState,
                firstTimeInMap: action?.data,
            };
        case 'SET_MY_CLUBS':
            return {
                ...prevState,
                myOwnClubs: action?.data,
            };
        case 'SET_LONG_PRESS_CHANNEL':
            return {
                ...prevState,
                selectedChannel: action?.data,
            };
        case 'SET_DELETE_CHANNEL_POPUP':
            return {
                ...prevState,
                deletePopup: action?.data,
            };
        case 'SET_DELETE_POPUP_LOADER':
            return {
                ...prevState,
                deletePopupLoader: action?.data,
            };
        case 'SET_COUNTRY':
            return {
                ...prevState,
                country: action?.data,
            };
        case 'SET_STATE':
            return {
                ...prevState,
                state: action?.data,
            };
        case 'SET_CITY':
            return {
                ...prevState,
                city: action?.data,
            };
        case 'SET_CROSS_ICON_TOGGLE':
            return {
                ...prevState,
                isCrossIconToggle: action?.data,
            };
        case 'SET_POLL_DATA':
            return {
                ...prevState,
                poll: action?.data,
            };
        case 'SET_GUIDE_STEP':
            return {
                ...prevState,
                guideStep: action?.data,
            };
        case 'SET_START_GUIDE':
            return {
                ...prevState,
                startGuide: action?.data,
            };
        case 'SET_NOTIFICATIONS':
            return {
                ...prevState,
                notifications: action?.data,
            };
        case 'SET_STATS':
            return {
                ...prevState,
                stats: action?.data,
            };
        case 'SET_MUTE_POPUP':
            return {
                ...prevState,
                mutePopup: action?.data,
            };
        case 'SET_IS_FIRST_TIME_VISIT_HOME':
            return {
                ...prevState,
                isFirstTimeVisitHome: action?.data,
            };
        case 'SET_MENU_BOTTOM_SHEET_OPEN':
            return {
                ...prevState,
                isMenuBottomSheetOpen: action?.data,
            };
        case 'SET_MENU_BOTTOM_SHEET_OPEN_INDEX':
            return {
                ...prevState,
                isMenuBottomSheetOpenIndex: action?.data,
            };
        case 'SET_EXPANDED_NOTIFICATION':
            return {
                ...prevState,
                expanded: action?.data,
            };
        case 'SET_TEAL_DOT_STATUS':
            return {
                ...prevState,
                tealDotStatus: action?.data,
            };
        case 'SET_APP_LOADER':
            return {
                ...prevState,
                appLoader: action?.data,
            };
        case 'SET_SELECTED_OFFER':
            return {
                ...prevState,
                selectedOffer: action?.data,
            };
        case 'SET_OFFLINE_LOG_GAME_STEP':
            return {
                ...prevState,
                offlineLogGameStep: action?.data,
            };
        case 'SET_RECEIVED_OPEN':
            return {
                ...prevState,
                receivedOpen: action?.data,
            };
        case 'SET_REQUESTED_OPEN':
            return {
                ...prevState,
                requestedOpen: action?.data,
            };
        case 'SET_REQUESTED_ACCEPTED':
            return {
                ...prevState,
                requestedAccepted: action?.data,
            };
        case 'SET_RECEIVED_ACCEPTED':
            return {
                ...prevState,
                receivedAccepted: action?.data,
            };
        case 'SET_ALL_ACCEPTED_REQUESTS':
            return {
                ...prevState,
                allAcceptedRequests: action?.data,
            };
        case 'SET_APP_SKELTON_LOADER':
            return {
                ...prevState,
                appSkeltonLoader: action?.data,
            };
        case 'SET_ALL_REQUEST_HOSTS_DATA':
            return {
                ...prevState,
                allRequestHostsData: action?.data,
            };
        case 'SET_UNREAD_CHANNELS_OBJECT':
            return {
                ...prevState,
                unreadChannelsObject: action?.data,
            };
        case 'SET_IS_MAP_VIEW_EXPANDED':
            return {
                ...prevState,
                isMapViewExpanded: action?.data,
            };
        case 'SET_SCREEN':
            return {
                ...prevState,
                screen: action?.data,
            };
        case 'SET_IS_MAP_SEARCH_ACTIVE':
            return {
                ...prevState,
                isMapSearchActive: action?.data,
            };
        case 'SET_MAP_BOUNDARIES':
            return {
                ...prevState,
                mapBoundaries: action?.data,
            };
        case 'SET_GAME_REVIEW':
            return {
                ...prevState,
                gameReview: action?.data,
            };
        case 'SET_SHOULD_DETAIL_BOTTOM_SHEET_OPEN':
            return {
                ...prevState,
                shouldDetailBottomSheetOpen: action?.data,
            };
        case 'SET_FILTER_ACTIVE':
            return {
                ...prevState,
                filterActive: action?.data,
            };
        case 'HOME_SCREEN_POPUP_STATE':
            return {
                ...prevState,
                homeScreenPopupSteps: action?.data,
            };
        case 'SET_REACTION_STATE':
            return {
                ...prevState,
                reactionState: action?.data,
            };
        default:
            return prevState;
    }
};
//
