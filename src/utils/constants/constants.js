import { Text } from "react-native";

const constants = {
    YEARLY_VALIDATION: {
        VERIFIED: 1,
        VERIFICATION_REQUIRED: 2,
    },
    CLEVERTAP: {
        PROFILE: {
            IDENTITY: 'Identity',
            EMAIL: 'Email',
            NAME: 'Name',
            PHONE: 'Phone',
            GENDER: 'Gender',
            GOLF_INDEX: 'Golf index',
            COUNTRY: 'Country',
            CLUBS: 'Clubs',
            DATE_OF_JOINING: 'Date of joining',
            CURRENT_MEMBERSHIP_PLAN: 'Current membership plan',
            TG_AMBASSADOR: 'TG ambassador',
            TIER: 'Tier',
            USER_VISIBILITY: 'User visibility',
            ACCOUNT_MUTED: 'Account muted',
            LAST_ONBOARDING_STEP_COMPLETED: 'Last Onboarding Step completed',
            STATE: 'State',
            CITY: 'City',
            POSTAL_CODE: 'Postal Code',
            PROMOCODE: 'Promocode',
            USERNAME: 'Username',
            NEWSLETTER_SUBSCRIPTION: 'Newsletter Subscription',
            RESENT_VERIFICATION_CODE: 'Resend Otp Step 1',
        },
        EVENTS: {
            TG_GROUPS_ON_MAP: 'TG Groups on map',
            GOLF_CLUBS: 'Golf Clubs',
            GET_VERIFICATION_CODE_STEP_1: 'Get Verification Code Step 1',
            VERIFY_OTP: 'Verify Otp',
            PERSONAL_PROFILE_CONTINUE_STEP_2: 'Personal Profile Continue Step 2',

            PERSONAL_PROFILE_LOGOUT_STEP_2: 'Personal Profile Logout Step 2',
            PHONE_NUMBER_VERIFIED_STEP_1: 'Phone Number Verified Step 1',
            GOLFER_PROFILE_CONTINUE_STEP_3: 'Golfer Profile Continue Step 3',
            GOLFER_PROFILE_LOGOUT_STEP_3: 'Golfer Profile Logout Step 3',
            ADD_CLUB_CONTINUE_STEP_4: 'Add Club Continue Step 4',
            ADD_CLUB_LOGOUT_STEP_4: 'Add Club Logout Step 4',
            PROFILE_OVERVIEW_CONTINUE_STEP_5: 'Profile Overview Continue Step 5',
            PROFILE_OVERVIEW_LOGOUT_STEP_5: 'Profile Overview Logout Step 5',
            ACCOUNT_SETTINGS_DELETE_ACCOUNT: 'Account Settings - Delete Account',
            DELETE_ACCOUNT_FIRST_PAGE: 'Delete Account - First page Continue',
            DELETE_ACCOUNT_SECOND_PAGE: 'Delete Account - Second page Delete Account',
            DELETE_ACCOUNT_CONFIRMATION_DELETE_ACCOUNT: 'Delete Account - confirmation Delete Account',
            REQUESTS_CREATE_REQUEST: 'Requests > Create Request',
            REQUESTS_POST_NEW_REQUEST: 'Requests > Post new request',
        },
        CLICK_PEGBOARD: "Click pegboard",
        CLICK_OFFERS: "Click Offers",
        CLICK_EVENTS: "Click Events",
        CLICK_BENEFITS: "Click benefits",
        CLICK_FEED: "Click feed",
        CLICK_REQUESTS: "Click Requests",
        CLICK_CHAT: " Click Chat",
        CLICK_HELP: "Click help",
        CLICK_FRIENDS: "Click Friends",
        CLICK_GROUPS: "Click groups",
        CLICK_HOME: "Click Home",
        CLICK_CLUBS: "Click Clubs",
        CLICK_ON_PLAYING_CARD: " Click on Playing Card",
        OWN_PROFILE_HEADER: "Own Profile Header",
        CLICK_MAP_PIN: "Click Map Pin",
        CREATE_POLL: "Create Poll",
        CREATE_POLLS: "Create Polls",
        CLICK_CLUB_CARD: "Click on Club Card in List",
        CLICK_FAVORITE_CLUB: "Click on Favorite option on Club Card in List",
        CLICK_PLAYED_CLUB: "Click on Played option on Club Card in List",
        Club_Filter_Applied: "Club Filter Applied",
        FRIENDS_PLAYED_CLUB: "View Friends Played in Club",
        GROUP_MEMBERS: "View Group members in Club",
        FRIENDS_IN_THE_CLUB: "View Friends in Club",
        OFFER_CARD: "Offer card",
        REQUEST_AGAINST_OFFERS: "Request Against Offer",
        VIEW_ALL: "View All",
        CREATE_REQUEST_FROM_MAP: "Click on Create Request from Map",
        CLICK_OFFER_IN_CLUB_DETAILS: "Click Offer in Club Details",
        CLICK_ON_CREATE_REQUEST_FROM_MAP: "Click on Create Request from Map",
    },
    ImageSize: {
        128: 128,
        256: 256,
        512: 512,
        1024: 1024,
        1280: 1280,
    },
};
export const tiers = [, 'Fern', 'Sage', 'Moss', , 'Olive'];

export const supportedReactions = [
    { type: 'haha', Icon: () => <Text>😂</Text> },
    { type: 'sad', Icon: () => <Text>😔</Text> },
    { type: 'love', Icon: () => <Text>❤️</Text> },
    { type: 'like', Icon: () => <Text>👍</Text> },
    { type: 'wow', Icon: () => <Text>😮</Text> },
];

export default constants;
