import { check, openSettings, request, RESULTS } from 'react-native-permissions';

import { Platform, PermissionsAndroid } from 'react-native';
import { PERMISSIONS } from 'react-native-permissions';
import { Alert } from 'react-native';

// Android-specific camera permission handler
const requestAndroidCameraPermission = async (): Promise<boolean> => {
    try {
        const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.CAMERA,
            {
                title: 'Camera Permission',
                message: 'This app needs access to your camera to take photos.',
                buttonNeutral: 'Ask Me Later',
                buttonNegative: 'Cancel',
                buttonPositive: 'OK',
            }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (error) {
        console.error('Error requesting Android camera permission:', error);
        return false;
    }
};

// Check Android storage permission for camera (needed for saving captured images)
const checkAndroidStoragePermission = async (): Promise<boolean> => {
    try {
        // For Android 13+ (API 33+), we need READ_MEDIA_IMAGES
        // For older versions, we need WRITE_EXTERNAL_STORAGE
        const storagePermission = Platform.Version >= 33
            ? PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
            : PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE;

        const hasStoragePermission = await PermissionsAndroid.check(storagePermission);
        if (!hasStoragePermission) {
            const granted = await PermissionsAndroid.request(storagePermission, {
                title: 'Storage Permission',
                message: 'This app needs access to storage to save captured photos.',
                buttonNeutral: 'Ask Me Later',
                buttonNegative: 'Cancel',
                buttonPositive: 'OK',
            });
            return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
        return true;
    } catch (error) {
        console.error('Error checking Android storage permission:', error);
        return false;
    }
};

// Check Android camera permission
const checkAndroidCameraPermission = async (): Promise<boolean> => {
    try {
        console.log('🔍 Checking Android camera permissions...');

        // Check camera permission
        const hasCameraPermission = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA);
        console.log('📷 Camera permission status:', hasCameraPermission);

        if (!hasCameraPermission) {
            console.log('📷 Requesting camera permission...');
            const cameraGranted = await requestAndroidCameraPermission();
            if (!cameraGranted) {
                console.error('❌ Camera permission denied');
                return false;
            }
        }

        // Check storage permission (needed for saving captured images)
        console.log('💾 Checking storage permission...');
        const hasStoragePermission = await checkAndroidStoragePermission();
        console.log('💾 Storage permission status:', hasStoragePermission);

        if (!hasStoragePermission) {
            console.error('❌ Storage permission denied');
            return false;
        }

        console.log('✅ All Android camera permissions granted');
        return true;
    } catch (error) {
        console.error('Error checking Android camera permission:', error);
        return false;
    }
};

export const checkAndRequestPermission = async (permissionType: 'camera' | 'gallery'): Promise<boolean> => {
    // Special handling for Android camera permissions
    if (Platform.OS === 'android' && permissionType === 'camera') {
        return await checkAndroidCameraPermission();
    }

    const permission = Platform.select({
        ios: permissionType === 'camera' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: permissionType === 'camera' ? PERMISSIONS.ANDROID.CAMERA : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
    });

    if (!permission) {
        return false;
    }

    try {
        const permissionStatus = await check(permission);
        switch (permissionStatus) {
            case RESULTS.GRANTED:
                return true;

            case RESULTS.DENIED:
            case RESULTS.UNAVAILABLE:
                const requestResult = await request(permission);
                return requestResult === RESULTS.GRANTED;

            case RESULTS.BLOCKED:
                Alert.alert(
                    `${permissionType.charAt(0).toUpperCase() + permissionType.slice(1)} Permission Required`,
                    `Please enable ${permissionType} access in your device settings to continue.`,
                    [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Open Settings', onPress: () => openSettings() },
                    ],
                );
                return false;

            default:
                return false;
        }
    } catch (error) {
        console.error(`Error checking ${permissionType} permission:`, error);
        return false;
    }
};
