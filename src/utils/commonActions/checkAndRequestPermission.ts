import { check, openSettings, request, RESULTS } from 'react-native-permissions';

import { Platform } from 'react-native';
import { PERMISSIONS } from 'react-native-permissions';
import { Alert } from 'react-native';

export const checkAndRequestPermission = async (permissionType: 'camera' | 'gallery'): Promise<boolean> => {
    const permission = Platform.select({
        ios: permissionType === 'camera' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: permissionType === 'camera' ? PERMISSIONS.ANDROID.CAMERA : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
    });

    if (!permission) {
        return false;
    }

    try {
        const permissionStatus = await check(permission);
        switch (permissionStatus) {
            case RESULTS.GRANTED:
                return true;

            case RESULTS.DENIED:
            case RESULTS.UNAVAILABLE:
                const requestResult = await request(permission);
                return requestResult === RESULTS.GRANTED;

            case RESULTS.BLOCKED:
                Alert.alert(
                    `${permissionType.charAt(0).toUpperCase() + permissionType.slice(1)} Permission Required`,
                    `Please enable ${permissionType} access in your device settings to continue.`,
                    [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Open Settings', onPress: () => openSettings() },
                    ],
                );
                return false;

            default:
                return false;
        }
    } catch (error) {
        console.error(`Error checking ${permissionType} permission:`, error);
        return false;
    }
};
