import ImagePicker from 'react-native-image-crop-picker';

import { checkAndRequestPermission } from './checkAndRequestPermission';
import { Alert, Platform } from 'react-native';

export const pickImageFromGalleryWithCrop = async (
    options = {
        width: 800,
        height: 800,
        cropping: true,
        cropperCircleOverlay: false,
        mediaType: 'photo' as 'photo' | 'video' | 'any',
        freeStyleCropEnabled: false,
        cropperToolbarTitle: 'Crop Profile Photo',
        cropperCancelText: 'Cancel',
        cropperChooseText: 'Choose',
        includeBase64: false,
        compressImageMaxWidth: 1024,
        compressImageMaxHeight: 1024,
        compressImageQuality: 0.8,
    },
): Promise<{
    path: string;
    size: number;
    width: number;
    height: number;
    mime: string;
}> => {
    try {
        if (Platform.OS === 'android') {
            const hasPermission = await checkAndRequestPermission('gallery');
            if (!hasPermission) {
                throw new Error('Gallery permission not granted');
            }
        }

        const image = await ImagePicker.openPicker({
            width: options.width,
            height: options.height,
            cropping: options.cropping,
            cropperCircleOverlay: options.cropperCircleOverlay,
            mediaType: options.mediaType,
            freeStyleCropEnabled: options.freeStyleCropEnabled,
            cropperToolbarTitle: options.cropperToolbarTitle,
            cropperCancelText: options.cropperCancelText,
            cropperChooseText: options.cropperChooseText,
            includeBase64: options.includeBase64,
            compressImageMaxWidth: options.compressImageMaxWidth,
            compressImageMaxHeight: options.compressImageMaxHeight,
            compressImageQuality: options.compressImageQuality,
        });

        // Check file size in MB
        const fileSizeInMB = image.size / (1024 * 1024);
        if (fileSizeInMB > 5) {
            Alert.alert('Image size should not exceed 5MB');
            throw new Error('Image size should not exceed 5MB');
        }

        return image;
    } catch (error) {
        console.error('Error picking image from gallery: ', error);
        throw error;
    }
};
