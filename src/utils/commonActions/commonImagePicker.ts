import ImagePicker from 'react-native-image-crop-picker';
import { Image as ImageCompressor } from 'react-native-compressor';

import { checkAndRequestPermission } from './checkAndRequestPermission';
import { Alert, Platform } from 'react-native';

// Common image picker options interface
interface ImagePickerOptions {
    width?: number;
    height?: number;
    cropping?: boolean;
    cropperCircleOverlay?: boolean;
    mediaType?: 'photo' | 'video' | 'any';
    freeStyleCropEnabled?: boolean;
    cropperToolbarTitle?: string;
    cropperCancelText?: string;
    cropperChooseText?: string;
    includeBase64?: boolean;
    compressImageMaxWidth?: number;
    compressImageMaxHeight?: number;
    compressImageQuality?: number;
}

// Common image response interface
interface ImageResponse {
    path: string;
    size: number;
    width: number;
    height: number;
    mime: string;
}

// Default options for image picker
const defaultOptions: ImagePickerOptions = {
    width: 800,
    height: 800,
    cropping: true,
    cropperCircleOverlay: false,
    mediaType: 'photo',
    freeStyleCropEnabled: false,
    cropperToolbarTitle: 'Crop Photo',
    cropperCancelText: 'Cancel',
    cropperChooseText: 'Choose',
    includeBase64: false,
    compressImageMaxWidth: 1024,
    compressImageMaxHeight: 1024,
    compressImageQuality: 0.8,
};

export const pickImageFromGalleryWithCrop = async (
    options: ImagePickerOptions = defaultOptions,
): Promise<ImageResponse> => {
    try {
        if (Platform.OS === 'android') {
            const hasPermission = await checkAndRequestPermission('gallery');
            if (!hasPermission) {
                throw new Error('Gallery permission not granted');
            }
        }

        const mergedOptions = { ...defaultOptions, ...options };

        const image = await ImagePicker.openPicker({
            width: mergedOptions.width,
            height: mergedOptions.height,
            cropping: mergedOptions.cropping,
            cropperCircleOverlay: mergedOptions.cropperCircleOverlay,
            mediaType: mergedOptions.mediaType,
            freeStyleCropEnabled: mergedOptions.freeStyleCropEnabled,
            cropperToolbarTitle: mergedOptions.cropperToolbarTitle,
            cropperCancelText: mergedOptions.cropperCancelText,
            cropperChooseText: mergedOptions.cropperChooseText,
            includeBase64: mergedOptions.includeBase64,
            compressImageMaxWidth: mergedOptions.compressImageMaxWidth,
            compressImageMaxHeight: mergedOptions.compressImageMaxHeight,
            compressImageQuality: mergedOptions.compressImageQuality,
        });

        // Check file size in MB
        const fileSizeInMB = image.size / (1024 * 1024);
        if (fileSizeInMB > 5) {
            Alert.alert('Image size should not exceed 5MB');
            throw new Error('Image size should not exceed 5MB');
        }

        return image;
    } catch (error) {
        console.error('Error picking image from gallery: ', error);
        throw error;
    }
};

export const launchCameraWithCrop = async (
    options: ImagePickerOptions = defaultOptions,
): Promise<ImageResponse> => {
    try {
        const hasPermission = await checkAndRequestPermission('camera');
        if (!hasPermission) {
            throw new Error('Camera permission not granted');
        }

        const mergedOptions = { ...defaultOptions, ...options };

        const image = await ImagePicker.openCamera({
            width: mergedOptions.width,
            height: mergedOptions.height,
            cropping: mergedOptions.cropping,
            cropperCircleOverlay: mergedOptions.cropperCircleOverlay,
            mediaType: mergedOptions.mediaType,
            freeStyleCropEnabled: mergedOptions.freeStyleCropEnabled,
            cropperToolbarTitle: mergedOptions.cropperToolbarTitle,
            cropperCancelText: mergedOptions.cropperCancelText,
            cropperChooseText: mergedOptions.cropperChooseText,
            includeBase64: mergedOptions.includeBase64,
            compressImageMaxWidth: mergedOptions.compressImageMaxWidth,
            compressImageMaxHeight: mergedOptions.compressImageMaxHeight,
            compressImageQuality: mergedOptions.compressImageQuality,
        });

        // Check file size in MB
        const fileSizeInMB = image.size / (1024 * 1024);
        if (fileSizeInMB > 5) {
            Alert.alert('Image size should not exceed 5MB');
            throw new Error('Image size should not exceed 5MB');
        }

        return image;
    } catch (error) {
        console.error('Error launching camera: ', error);
        throw error;
    }
};

// Image compression options interface
interface CompressionOptions {
    compressionMethod?: 'auto' | 'manual';
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
}

// Default compression options
const defaultCompressionOptions: CompressionOptions = {
    compressionMethod: 'manual',
    maxWidth: 1280,
    quality: 0.8,
};

export const compressImage = async (
    imageUri: string,
    options: CompressionOptions = defaultCompressionOptions,
): Promise<string> => {
    try {
        const mergedOptions = { ...defaultCompressionOptions, ...options };

        const compressedUri = await ImageCompressor.compress(imageUri, {
            compressionMethod: mergedOptions.compressionMethod,
            maxWidth: mergedOptions.maxWidth,
            maxHeight: mergedOptions.maxHeight,
            quality: mergedOptions.quality,
        });

        return compressedUri;
    } catch (error) {
        console.error('Error compressing image: ', error);
        throw error;
    }
};

// Combined function to pick image from gallery and compress it
export const pickAndCompressImageFromGallery = async (
    pickerOptions: ImagePickerOptions = defaultOptions,
    compressionOptions: CompressionOptions = defaultCompressionOptions,
): Promise<{ originalImage: ImageResponse; compressedUri: string }> => {
    try {
        const originalImage = await pickImageFromGalleryWithCrop(pickerOptions);
        const compressedUri = await compressImage(originalImage.path, compressionOptions);

        return {
            originalImage,
            compressedUri,
        };
    } catch (error) {
        console.error('Error picking and compressing image from gallery: ', error);
        throw error;
    }
};

// Combined function to launch camera and compress the captured image
export const launchCameraAndCompressImage = async (
    pickerOptions: ImagePickerOptions = defaultOptions,
    compressionOptions: CompressionOptions = defaultCompressionOptions,
): Promise<{ originalImage: ImageResponse; compressedUri: string }> => {
    try {
        const originalImage = await launchCameraWithCrop(pickerOptions);
        const compressedUri = await compressImage(originalImage.path, compressionOptions);

        return {
            originalImage,
            compressedUri,
        };
    } catch (error) {
        console.error('Error launching camera and compressing image: ', error);
        throw error;
    }
};
