import ImagePicker from 'react-native-image-crop-picker';
import { Image as ImageCompressor } from 'react-native-compressor';

import { checkAndRequestPermission } from './checkAndRequestPermission';
import { Alert, Platform, PermissionsAndroid } from 'react-native';

// Common image picker options interface
interface ImagePickerOptions {
    width?: number;
    height?: number;
    cropping?: boolean;
    cropperCircleOverlay?: boolean;
    mediaType?: 'photo' | 'video' | 'any';
    freeStyleCropEnabled?: boolean;
    cropperToolbarTitle?: string;
    cropperCancelText?: string;
    cropperChooseText?: string;
    includeBase64?: boolean;
    compressImageMaxWidth?: number;
    compressImageMaxHeight?: number;
    compressImageQuality?: number;
}

// Common image response interface
interface ImageResponse {
    path: string;
    size: number;
    width: number;
    height: number;
    mime: string;
}

// Default options for image picker
const defaultOptions: ImagePickerOptions = {
    width: 800,
    height: 800,
    cropping: true,
    cropperCircleOverlay: false,
    mediaType: 'photo',
    freeStyleCropEnabled: false,
    cropperToolbarTitle: 'Crop Photo',
    cropperCancelText: 'Cancel',
    cropperChooseText: 'Choose',
    includeBase64: false,
    compressImageMaxWidth: 1024,
    compressImageMaxHeight: 1024,
    compressImageQuality: 0.8,
};

export const pickImageFromGalleryWithCrop = async (
    options: ImagePickerOptions = defaultOptions,
): Promise<ImageResponse> => {
    try {
        if (Platform.OS === 'android') {
            const hasPermission = await checkAndRequestPermission('gallery');
            if (!hasPermission) {
                throw new Error('Gallery permission not granted');
            }
        }

        const mergedOptions = { ...defaultOptions, ...options };

        const image = await ImagePicker.openPicker({
            width: mergedOptions.width,
            height: mergedOptions.height,
            cropping: mergedOptions.cropping,
            cropperCircleOverlay: mergedOptions.cropperCircleOverlay,
            mediaType: mergedOptions.mediaType,
            freeStyleCropEnabled: mergedOptions.freeStyleCropEnabled,
            cropperToolbarTitle: mergedOptions.cropperToolbarTitle,
            cropperCancelText: mergedOptions.cropperCancelText,
            cropperChooseText: mergedOptions.cropperChooseText,
            includeBase64: mergedOptions.includeBase64,
            compressImageMaxWidth: mergedOptions.compressImageMaxWidth,
            compressImageMaxHeight: mergedOptions.compressImageMaxHeight,
            compressImageQuality: mergedOptions.compressImageQuality,
        });

        // Check file size in MB
        const fileSizeInMB = image.size / (1024 * 1024);
        if (fileSizeInMB > 5) {
            Alert.alert('Image size should not exceed 5MB');
            throw new Error('Image size should not exceed 5MB');
        }

        return image;
    } catch (error) {
        console.error('Error picking image from gallery: ', error);
        throw error;
    }
};

export const launchCameraWithCrop = async (
    options: ImagePickerOptions = defaultOptions,
): Promise<ImageResponse> => {
    try {
        console.log('🔍 Starting camera launch process...');

        const hasPermission = await checkAndRequestPermission('camera');
        console.log('📷 Camera permission result:', hasPermission);

        if (!hasPermission) {
            console.error('❌ Camera permission denied');
            throw new Error('Camera permission not granted');
        }

        const mergedOptions = { ...defaultOptions, ...options };
        console.log('⚙️ Camera options:', mergedOptions);

        // Add delay for Android to ensure permission is properly granted
        if (Platform.OS === 'android') {
            console.log('⏳ Adding Android delay...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Double-check camera permission on Android
            console.log('🔍 Double-checking Android camera permission...');
            const androidCameraPermission = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA);
            console.log('📷 Android camera permission double-check:', androidCameraPermission);

            if (!androidCameraPermission) {
                console.error('❌ Android camera permission lost, requesting again...');
                const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA);
                if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
                    throw new Error('Camera permission was denied after double-check');
                }
            }
        }

        console.log('📸 Opening camera...');
        const image = await ImagePicker.openCamera({
            width: mergedOptions.width,
            height: mergedOptions.height,
            cropping: mergedOptions.cropping,
            cropperCircleOverlay: mergedOptions.cropperCircleOverlay,
            mediaType: mergedOptions.mediaType,
            freeStyleCropEnabled: mergedOptions.freeStyleCropEnabled,
            cropperToolbarTitle: mergedOptions.cropperToolbarTitle,
            cropperCancelText: mergedOptions.cropperCancelText,
            cropperChooseText: mergedOptions.cropperChooseText,
            includeBase64: mergedOptions.includeBase64,
            compressImageMaxWidth: mergedOptions.compressImageMaxWidth,
            compressImageMaxHeight: mergedOptions.compressImageMaxHeight,
            compressImageQuality: mergedOptions.compressImageQuality,
            // Android file path fixes
            useFrontCamera: false,
            writeTempFile: false, // Don't write to temp file to avoid path issues
            includeExif: false, // Disable EXIF to reduce complexity
            forceJpg: true, // Force JPG format for better compatibility
        });

        console.log('✅ Camera image captured:', {
            path: image.path,
            size: image.size,
            width: image.width,
            height: image.height
        });

        // Check file size in MB
        const fileSizeInMB = image.size / (1024 * 1024);
        if (fileSizeInMB > 5) {
            Alert.alert('Image size should not exceed 5MB');
            throw new Error('Image size should not exceed 5MB');
        }

        return image;
    } catch (error: any) {
        console.error('❌ Error launching camera:', error);
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            stack: error.stack
        });

        // Handle specific error cases
        if (error.code === 'camera_unavailable') {
            throw new Error('Camera is not available on this device');
        } else if (error.code === 'permission') {
            throw new Error('Camera permission was denied');
        } else if (error.message && error.message.includes('cancelled')) {
            throw new Error('User cancelled image selection');
        } else {
            throw new Error(`Camera error: ${error.message || 'Unknown error occurred'}`);
        }
    }
};

// Fallback camera function with minimal options for Android compatibility
export const launchCameraSimple = async (): Promise<ImageResponse> => {
    try {
        console.log('🔍 Starting simple camera launch...');

        // Check permissions
        const hasPermission = await checkAndRequestPermission('camera');
        if (!hasPermission) {
            throw new Error('Camera permission not granted');
        }

        // Add delay for Android
        if (Platform.OS === 'android') {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log('📸 Opening camera with minimal options...');
        const image = await ImagePicker.openCamera({
            width: 800,
            height: 800,
            cropping: false, // Disable cropping for simplicity
            mediaType: 'photo',
            compressImageQuality: 0.8,
            writeTempFile: false, // Avoid temp file path issues
            forceJpg: true, // Force JPG format
            includeExif: false, // Disable EXIF
        });

        console.log('✅ Simple camera image captured:', image);
        return image;
    } catch (error: any) {
        console.error('❌ Simple camera error:', error);
        throw error;
    }
};

// Alternative camera function that saves to external storage
export const launchCameraExternal = async (
    options: ImagePickerOptions = defaultOptions,
): Promise<ImageResponse> => {
    try {
        console.log('🔍 Starting external storage camera launch...');

        const hasPermission = await checkAndRequestPermission('camera');
        if (!hasPermission) {
            throw new Error('Camera permission not granted');
        }

        const mergedOptions = { ...defaultOptions, ...options };

        // Add delay for Android
        if (Platform.OS === 'android') {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log('📸 Opening camera with external storage...');
        const image = await ImagePicker.openCamera({
            width: mergedOptions.width,
            height: mergedOptions.height,
            cropping: mergedOptions.cropping,
            cropperCircleOverlay: mergedOptions.cropperCircleOverlay,
            mediaType: mergedOptions.mediaType,
            cropperToolbarTitle: mergedOptions.cropperToolbarTitle,
            compressImageQuality: mergedOptions.compressImageQuality,
            // Simplified options to avoid path issues
            writeTempFile: false,
            includeExif: false,
            forceJpg: true,
            // Try to use external storage
            path: Platform.OS === 'android' ? 'Pictures' : undefined,
        });

        console.log('✅ External camera image captured:', image);

        // Check file size
        const fileSizeInMB = image.size / (1024 * 1024);
        if (fileSizeInMB > 5) {
            Alert.alert('Image size should not exceed 5MB');
            throw new Error('Image size should not exceed 5MB');
        }

        return image;
    } catch (error: any) {
        console.error('❌ External camera error:', error);
        throw error;
    }
};

// Image compression options interface
interface CompressionOptions {
    compressionMethod?: 'auto' | 'manual';
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
}

// Default compression options
const defaultCompressionOptions: CompressionOptions = {
    compressionMethod: 'manual',
    maxWidth: 1280,
    quality: 0.8,
};

export const compressImage = async (
    imageUri: string,
    options: CompressionOptions = defaultCompressionOptions,
): Promise<string> => {
    try {
        const mergedOptions = { ...defaultCompressionOptions, ...options };

        const compressedUri = await ImageCompressor.compress(imageUri, {
            compressionMethod: mergedOptions.compressionMethod,
            maxWidth: mergedOptions.maxWidth,
            maxHeight: mergedOptions.maxHeight,
            quality: mergedOptions.quality,
        });

        return compressedUri;
    } catch (error) {
        console.error('Error compressing image: ', error);
        throw error;
    }
};

// Combined function to pick image from gallery and compress it
export const pickAndCompressImageFromGallery = async (
    pickerOptions: ImagePickerOptions = defaultOptions,
    compressionOptions: CompressionOptions = defaultCompressionOptions,
): Promise<{ originalImage: ImageResponse; compressedUri: string }> => {
    try {
        const originalImage = await pickImageFromGalleryWithCrop(pickerOptions);
        const compressedUri = await compressImage(originalImage.path, compressionOptions);

        return {
            originalImage,
            compressedUri,
        };
    } catch (error) {
        console.error('Error picking and compressing image from gallery: ', error);
        throw error;
    }
};

// Combined function to launch camera and compress the captured image
export const launchCameraAndCompressImage = async (
    pickerOptions: ImagePickerOptions = defaultOptions,
    compressionOptions: CompressionOptions = defaultCompressionOptions,
): Promise<{ originalImage: ImageResponse; compressedUri: string }> => {
    try {
        const originalImage = await launchCameraWithCrop(pickerOptions);
        const compressedUri = await compressImage(originalImage.path, compressionOptions);

        return {
            originalImage,
            compressedUri,
        };
    } catch (error) {
        console.error('Error launching camera and compressing image: ', error);
        throw error;
    }
};
