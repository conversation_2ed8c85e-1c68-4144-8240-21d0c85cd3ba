# Common Image Picker & Camera Utilities

A comprehensive React Native image picking and camera utility system with enhanced Android support, automatic compression, and robust error handling.

## 🚀 Features

- ✅ **Gallery Image Picker** with cropping support
- ✅ **Camera Launcher** with Android file path fixes
- ✅ **Image Compression** utility with configurable options
- ✅ **Combined Functions** for pick/capture + compress in one step
- ✅ **Cross-Platform Permission Handling** (iOS & Android)
- ✅ **File Size Validation** (max 5MB with user alerts)
- ✅ **TypeScript Support** with proper interfaces
- ✅ **Android FileProvider Configuration** for file access
- ✅ **Multiple Fallback Methods** for maximum compatibility

## 📦 Dependencies

```json
{
  "react-native-image-crop-picker": "^0.40.3",
  "react-native-image-picker": "^7.1.0",
  "react-native-compressor": "^1.8.24",
  "react-native-permissions": "^4.1.5"
}
```

## 🛠️ Setup Requirements

### Android Configuration

**1. FileProvider Setup (REQUIRED for Android camera)**

Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<provider
    android:name="androidx.core.content.FileProvider"
    android:authorities="${applicationId}.provider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
</provider>
```

**2. Create `android/app/src/main/res/xml/file_paths.xml`:**
```xml
<?xml version="1.0" encoding="utf-8"?>
<paths xmlns:android="http://schemas.android.com/apk/res/android">
    <files-path name="files" path="." />
    <external-path name="external" path="." />
    <external-files-path name="external_files" path="." />
    <cache-path name="cache" path="." />
    <external-cache-path name="external_cache" path="." />
    <external-files-path name="pictures" path="Pictures" />
    <external-files-path name="camera" path="Pictures/Camera" />
</paths>
```

**3. Rebuild Android after FileProvider setup:**
```bash
cd android && ./gradlew clean && cd .. && npx react-native run-android
```

## 📚 Available Functions

### Core Functions

| Function | Description | Platform Support |
|----------|-------------|-------------------|
| `pickImageFromGalleryWithCrop` | Gallery picker with cropping | iOS & Android |
| `launchCameraWithCrop` | Camera with cropping (enhanced) | iOS & Android |
| `launchCameraNative` | Native camera (file path safe) | iOS & Android |
| `compressImage` | Standalone image compression | iOS & Android |
| `pickAndCompressImageFromGallery` | Gallery + compression | iOS & Android |
| `launchCameraAndCompressImage` | Camera + compression | iOS & Android |

### Permission Function

| Function | Description |
|----------|-------------|
| `checkAndRequestPermission` | Handle camera/gallery permissions |

## 🎯 Usage Examples

### Basic Gallery Picker
```javascript
import { pickImageFromGalleryWithCrop } from '../utils/commonActions';

const selectFromGallery = async () => {
    try {
        const image = await pickImageFromGalleryWithCrop({
            width: 800,
            height: 800,
            cropping: true,
            cropperCircleOverlay: false,
            compressImageQuality: 0.8,
        });
        
        console.log('Selected image:', image);
        // Use image.path for the image URI
    } catch (error) {
        if (!error.message.includes('cancelled')) {
            Alert.alert('Error', 'Failed to select image');
        }
    }
};
```

### Camera Launch (Recommended)
```javascript
import { launchCameraNative, compressImage } from '../utils/commonActions';

const openCamera = async () => {
    try {
        // For Android: Use native camera (avoids file path issues)
        const image = await launchCameraNative();
        
        // Compress the image
        const compressedUri = await compressImage(image.path, {
            compressionMethod: 'manual',
            maxWidth: 1280,
            quality: 0.8,
        });
        
        console.log('Camera image:', image);
        console.log('Compressed URI:', compressedUri);
        // Use compressedUri for upload
    } catch (error) {
        if (!error.message.includes('cancelled')) {
            Alert.alert('Error', 'Failed to capture image');
        }
    }
};
```

### Combined Camera + Compression
```javascript
import { launchCameraAndCompressImage } from '../utils/commonActions';

const cameraWithCompression = async () => {
    try {
        const result = await launchCameraAndCompressImage(
            {
                width: 1024,
                height: 1024,
                cropping: true,
                cropperCircleOverlay: false,
                compressImageQuality: 0.9,
            },
            {
                compressionMethod: 'manual',
                maxWidth: 1280,
                quality: 0.8,
            }
        );
        
        console.log('Original image:', result.originalImage);
        console.log('Compressed URI:', result.compressedUri);
        // Use result.compressedUri for upload
    } catch (error) {
        if (!error.message.includes('cancelled')) {
            Alert.alert('Error', 'Failed to capture and compress image');
        }
    }
};
```

### Profile Photo Picker (Circular Crop)
```javascript
import { pickAndCompressImageFromGallery } from '../utils/commonActions';

const selectProfilePhoto = async () => {
    try {
        const result = await pickAndCompressImageFromGallery(
            {
                width: 400,
                height: 400,
                cropping: true,
                cropperCircleOverlay: true, // Circular crop
                cropperToolbarTitle: 'Crop Profile Photo',
                compressImageQuality: 0.8,
            },
            {
                compressionMethod: 'manual',
                maxWidth: 800,
                quality: 0.8,
            }
        );
        
        return result.compressedUri;
    } catch (error) {
        console.error('Profile photo selection failed:', error);
        throw error;
    }
};
```

### Manual Permission Check
```javascript
import { checkAndRequestPermission } from '../utils/commonActions';

const checkCameraPermission = async () => {
    try {
        const hasPermission = await checkAndRequestPermission('camera');
        if (hasPermission) {
            console.log('Camera permission granted');
            // Proceed with camera functionality
        } else {
            Alert.alert('Permission Required', 'Camera access is needed');
        }
    } catch (error) {
        console.error('Permission check failed:', error);
    }
};
```

## ⚙️ Configuration Options

### ImagePickerOptions
```typescript
interface ImagePickerOptions {
    width?: number;                    // Default: 800
    height?: number;                   // Default: 800
    cropping?: boolean;                // Default: true
    cropperCircleOverlay?: boolean;    // Default: false
    mediaType?: 'photo' | 'video' | 'any'; // Default: 'photo'
    freeStyleCropEnabled?: boolean;    // Default: false
    cropperToolbarTitle?: string;      // Default: 'Crop Photo'
    cropperCancelText?: string;        // Default: 'Cancel'
    cropperChooseText?: string;        // Default: 'Choose'
    includeBase64?: boolean;           // Default: false
    compressImageMaxWidth?: number;    // Default: 1024
    compressImageMaxHeight?: number;   // Default: 1024
    compressImageQuality?: number;     // Default: 0.8
}
```

### CompressionOptions
```typescript
interface CompressionOptions {
    compressionMethod?: 'auto' | 'manual'; // Default: 'manual'
    maxWidth?: number;                      // Default: 1280
    maxHeight?: number;                     // Default: undefined
    quality?: number;                       // Default: 0.8 (0.1-1.0)
}
```

### ImageResponse
```typescript
interface ImageResponse {
    path: string;    // File URI/path
    size: number;    // File size in bytes
    width: number;   // Image width in pixels
    height: number;  // Image height in pixels
    mime: string;    // MIME type (e.g., 'image/jpeg')
}
```

## 🎨 Common Use Cases

### 1. Group Icon (Circular Crop)
```javascript
const selectGroupIcon = async () => {
    const result = await pickAndCompressImageFromGallery(
        {
            width: 512,
            height: 512,
            cropping: true,
            cropperCircleOverlay: true,
            cropperToolbarTitle: 'Crop Group Icon',
        },
        { maxWidth: 512, quality: 0.8 }
    );
    return result.compressedUri;
};
```

### 2. Post Image (High Quality, No Crop)
```javascript
const selectPostImage = async () => {
    const result = await pickAndCompressImageFromGallery(
        {
            width: 1920,
            height: 1920,
            cropping: false, // No cropping for posts
            compressImageQuality: 0.9,
        },
        { maxWidth: 1920, quality: 0.85 }
    );
    return result.compressedUri;
};
```

### 3. Document/ID Photo (Square Crop)
```javascript
const selectDocumentPhoto = async () => {
    const result = await launchCameraAndCompressImage(
        {
            width: 800,
            height: 800,
            cropping: true,
            cropperCircleOverlay: false,
            cropperToolbarTitle: 'Crop Document',
        },
        { maxWidth: 1024, quality: 0.9 }
    );
    return result.compressedUri;
};
```

### 4. Avatar/Profile Picture
```javascript
const selectAvatar = async () => {
    const result = await pickAndCompressImageFromGallery(
        {
            width: 300,
            height: 300,
            cropping: true,
            cropperCircleOverlay: true,
            cropperToolbarTitle: 'Crop Avatar',
        },
        { maxWidth: 300, quality: 0.8 }
    );
    return result.compressedUri;
};
```

## 🔧 Platform-Specific Recommendations

### iOS
- Use `launchCameraAndCompressImage` for best results
- All functions work reliably on iOS
- Permission handling is automatic

### Android
- **Recommended**: Use `launchCameraNative` for camera (avoids file path issues)
- Ensure FileProvider is configured before using camera
- Rebuild app after FileProvider setup

## 🚨 Troubleshooting

### Android Camera Issues

**Problem**: "failed to find configured root that contains /storage/emulated/0/..."
**Solution**:
1. Ensure FileProvider is added to AndroidManifest.xml
2. Create file_paths.xml with proper configurations
3. Clean and rebuild Android: `cd android && ./gradlew clean && cd .. && npx react-native run-android`

**Problem**: Camera permission denied
**Solution**:
1. Check device Settings > Apps > [Your App] > Permissions
2. Enable Camera and Storage permissions manually
3. Restart the app

**Problem**: Camera opens and closes immediately
**Solution**:
1. Use `launchCameraNative` instead of `launchCameraWithCrop`
2. Check console logs for permission issues
3. Ensure device has working camera (test on physical device)

### iOS Camera Issues

**Problem**: Camera permission not working
**Solution**:
1. Check Info.plist for camera usage description
2. Use `checkAndRequestPermission('camera')` before launching camera
3. Test on physical device (camera doesn't work in simulator)

## 📝 Error Handling Best Practices

```javascript
const handleImageSelection = async () => {
    try {
        const result = await pickAndCompressImageFromGallery(options);
        // Success - use result.compressedUri
        setImageUri(result.compressedUri);
    } catch (error) {
        // Check if user cancelled (normal behavior)
        if (error.message && error.message.includes('cancelled')) {
            console.log('User cancelled image selection');
            return; // Don't show error for cancellation
        }

        // Handle actual errors
        console.error('Image selection failed:', error);
        Alert.alert('Error', 'Failed to select image. Please try again.');
    }
};
```

## 🔄 Migration from Old Image Picker

If you're migrating from direct `react-native-image-crop-picker` usage:

### Before
```javascript
import ImagePicker from 'react-native-image-crop-picker';

ImagePicker.openCamera({
    width: 800,
    height: 800,
    cropping: true,
}).then(image => {
    // Use image
});
```

### After
```javascript
import { launchCameraNative, compressImage } from '../utils/commonActions';

const image = await launchCameraNative();
const compressedUri = await compressImage(image.path);
// Use compressedUri
```

## 📊 Performance Tips

1. **Use compression**: Always compress images before upload to reduce file size
2. **Choose appropriate dimensions**: Don't use unnecessarily large dimensions
3. **Quality settings**: Use 0.8 quality for good balance of size/quality
4. **Platform optimization**: Use recommended functions for each platform

## 🔐 Security Considerations

1. **Permissions**: Always check permissions before accessing camera/gallery
2. **File validation**: Validate file types and sizes before processing
3. **Temporary files**: Compressed images are stored in app's cache directory
4. **User privacy**: Handle permission denials gracefully

## 📱 Testing Checklist

- [ ] Test on physical iOS device
- [ ] Test on physical Android device
- [ ] Test camera permissions (grant/deny scenarios)
- [ ] Test gallery permissions
- [ ] Test user cancellation (should not show errors)
- [ ] Test file size validation (>5MB images)
- [ ] Test image compression quality
- [ ] Test circular cropping for profile photos
- [ ] Test without cropping for posts
- [ ] Verify FileProvider configuration on Android
```
