# Android Camera Troubleshooting Guide

## Issue: "Failed to capture image from camera" on Android

This guide helps debug and fix Android camera issues with the enhanced commonImagePicker utilities.

## 🔍 Debugging Steps

### 1. Check Console Logs
Look for these log messages in your React Native debugger or device logs:

```
🔍 Starting camera launch process...
📷 Camera permission result: true/false
⚙️ Camera options: {...}
⏳ Adding Android delay...
🔍 Double-checking Android camera permission...
📷 Android camera permission double-check: true/false
📸 Opening camera...
✅ Camera image captured: {...}
```

### 2. Common Error Messages and Solutions

#### Error: "Camera permission not granted"
**Solution:** 
- Check if camera permission is declared in `android/app/src/main/AndroidManifest.xml`
- Ensure user granted permission when prompted
- Try manually enabling camera permission in device settings

#### Error: "Camera permission was denied after double-check"
**Solution:**
- User may have denied permission during the process
- Go to device Settings > Apps > [Your App] > Permissions > Camera > Allow

#### Error: "Camera is not available on this device"
**Solution:**
- Test on a physical device (camera doesn't work in emulator)
- Check if device has a working camera
- Try the simple camera function: `launchCameraSimple()`

#### Error: "Storage permission denied"
**Solution:**
- Android needs storage permission to save captured images
- Check storage permissions in device settings
- For Android 13+, ensure READ_MEDIA_IMAGES permission is granted

### 3. Test with Simple Camera Function

If the main camera function fails, try the simplified version:

```javascript
import { launchCameraSimple } from '../utils/commonActions';

const testSimpleCamera = async () => {
    try {
        const image = await launchCameraSimple();
        console.log('Simple camera worked:', image);
    } catch (error) {
        console.error('Simple camera failed:', error);
    }
};
```

### 4. Manual Permission Check

Add this test function to check permissions manually:

```javascript
import { PermissionsAndroid, Platform } from 'react-native';

const checkAllPermissions = async () => {
    if (Platform.OS === 'android') {
        const cameraPermission = await PermissionsAndroid.check(
            PermissionsAndroid.PERMISSIONS.CAMERA
        );
        
        const storagePermission = await PermissionsAndroid.check(
            Platform.Version >= 33 
                ? PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
                : PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
        );
        
        console.log('Camera permission:', cameraPermission);
        console.log('Storage permission:', storagePermission);
        
        return cameraPermission && storagePermission;
    }
    return true;
};
```

## 🛠️ Fixes Applied

### 1. Enhanced Permission Handling
- **Dual permission system**: Uses both `react-native-permissions` and `PermissionsAndroid`
- **Storage permissions**: Automatically requests storage access for saving images
- **Android version compatibility**: Handles different permission requirements for Android 13+

### 2. Android-Specific Optimizations
- **Permission double-check**: Verifies permissions right before camera launch
- **Increased delay**: 1000ms delay to ensure permissions are properly granted
- **Additional camera options**: Added `writeTempFile`, `includeExif`, `useFrontCamera: false`

### 3. Better Error Handling
- **Detailed logging**: Comprehensive console logs for debugging
- **Specific error messages**: Different error handling for different failure types
- **User-friendly alerts**: Clear error messages shown to users

### 4. Fallback Options
- **Simple camera function**: Minimal options version for compatibility
- **Graceful degradation**: Falls back to simpler options if advanced features fail

## 📱 Device Testing Checklist

### Before Testing:
- [ ] Test on physical Android device (not emulator)
- [ ] Ensure device has working camera
- [ ] Check Android version (affects permission requirements)
- [ ] Clear app data/cache if needed

### During Testing:
- [ ] Grant camera permission when prompted
- [ ] Grant storage permission when prompted
- [ ] Check console logs for detailed error information
- [ ] Try both gallery and camera functions

### If Still Failing:
- [ ] Try `launchCameraSimple()` function
- [ ] Manually check permissions in device settings
- [ ] Test with different camera options (disable cropping, etc.)
- [ ] Check if other camera apps work on the device

## 🔧 Advanced Debugging

### Enable Verbose Logging
The enhanced functions now include detailed console logging. Look for:
- Permission check results
- Camera options being used
- Success/failure at each step
- Detailed error information

### Test Different Camera Options
Try these variations if the default fails:

```javascript
// Minimal options
const minimalOptions = {
    width: 800,
    height: 800,
    cropping: false,
    mediaType: 'photo',
    compressImageQuality: 0.8,
};

// No compression
const noCompressionOptions = {
    width: 800,
    height: 800,
    cropping: true,
    compressImageQuality: 1.0, // No compression
};

// Square format
const squareOptions = {
    width: 800,
    height: 800,
    cropping: true,
    cropperCircleOverlay: false,
};
```

## 📞 Support

If camera still doesn't work after trying all these steps:

1. **Check device compatibility**: Some older Android devices may have issues
2. **Test on different devices**: Try on multiple Android devices/versions
3. **Review app permissions**: Ensure all required permissions are in AndroidManifest.xml
4. **Consider alternative libraries**: May need to use `react-native-image-picker` as fallback

## 🚀 Quick Fix Summary

The most common fix for Android camera issues:

1. **Grant permissions manually**: Go to Settings > Apps > [Your App] > Permissions
2. **Enable Camera and Storage permissions**
3. **Restart the app**
4. **Test camera function again**

The enhanced permission system should handle most cases automatically, but manual permission granting may be needed in some scenarios.
