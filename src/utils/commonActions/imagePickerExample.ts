/**
 * Example usage of the enhanced commonImagePicker utilities
 * This file demonstrates how to use the new camera and compression features
 */

import {
    pickImageFromGalleryWithCrop,
    launchCameraWithCrop,
    compressImage,
    pickAndCompressImageFromGallery,
    launchCameraAndCompressImage
} from './commonImagePicker';

// Example 1: Basic gallery picker with crop
export const exampleGalleryPicker = async () => {
    try {
        const image = await pickImageFromGalleryWithCrop({
            width: 800,
            height: 800,
            cropping: true,
            cropperCircleOverlay: false,
            compressImageQuality: 0.8,
        });
        
        console.log('Selected image:', image);
        return image;
    } catch (error) {
        console.error('Gallery picker error:', error);
        throw error;
    }
};

// Example 2: Basic camera launcher with crop
export const exampleCameraLauncher = async () => {
    try {
        const image = await launchCameraWithCrop({
            width: 1024,
            height: 1024,
            cropping: true,
            cropperCircleOverlay: true, // Circular crop for profile photos
            compressImageQuality: 0.9,
        });
        
        console.log('Captured image:', image);
        return image;
    } catch (error) {
        console.error('Camera launcher error:', error);
        throw error;
    }
};

// Example 3: Compress an existing image
export const exampleImageCompression = async (imageUri: string) => {
    try {
        const compressedUri = await compressImage(imageUri, {
            compressionMethod: 'manual',
            maxWidth: 1280,
            quality: 0.7,
        });
        
        console.log('Original URI:', imageUri);
        console.log('Compressed URI:', compressedUri);
        return compressedUri;
    } catch (error) {
        console.error('Image compression error:', error);
        throw error;
    }
};

// Example 4: Pick from gallery and compress in one step
export const exampleGalleryPickerWithCompression = async () => {
    try {
        const result = await pickAndCompressImageFromGallery(
            {
                width: 800,
                height: 800,
                cropping: true,
                compressImageQuality: 0.8,
            },
            {
                compressionMethod: 'manual',
                maxWidth: 1280,
                quality: 0.7,
            }
        );
        
        console.log('Original image:', result.originalImage);
        console.log('Compressed URI:', result.compressedUri);
        return result;
    } catch (error) {
        console.error('Gallery picker with compression error:', error);
        throw error;
    }
};

// Example 5: Launch camera and compress in one step
export const exampleCameraWithCompression = async () => {
    try {
        const result = await launchCameraAndCompressImage(
            {
                width: 1024,
                height: 1024,
                cropping: true,
                cropperCircleOverlay: false,
                compressImageQuality: 0.9,
            },
            {
                compressionMethod: 'manual',
                maxWidth: 1280,
                quality: 0.8,
            }
        );
        
        console.log('Original image:', result.originalImage);
        console.log('Compressed URI:', result.compressedUri);
        return result;
    } catch (error) {
        console.error('Camera with compression error:', error);
        throw error;
    }
};

// Example 6: Profile photo picker (circular crop)
export const exampleProfilePhotoPicker = async () => {
    try {
        const result = await pickAndCompressImageFromGallery(
            {
                width: 400,
                height: 400,
                cropping: true,
                cropperCircleOverlay: true, // Circular crop for profile photos
                cropperToolbarTitle: 'Crop Profile Photo',
                compressImageQuality: 0.8,
            },
            {
                compressionMethod: 'manual',
                maxWidth: 800,
                quality: 0.8,
            }
        );
        
        return result;
    } catch (error) {
        console.error('Profile photo picker error:', error);
        throw error;
    }
};

// Example 7: High quality image for posts
export const examplePostImagePicker = async () => {
    try {
        const result = await pickAndCompressImageFromGallery(
            {
                width: 1024,
                height: 1024,
                cropping: false, // No cropping for post images
                compressImageQuality: 0.9,
            },
            {
                compressionMethod: 'manual',
                maxWidth: 1920,
                quality: 0.85,
            }
        );
        
        return result;
    } catch (error) {
        console.error('Post image picker error:', error);
        throw error;
    }
};
