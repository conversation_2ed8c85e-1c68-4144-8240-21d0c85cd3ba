# Common Image Picker Utilities

This module provides enhanced image picking and compression utilities for the React Native app using `react-native-image-crop-picker` and `react-native-compressor`.

## Features

- ✅ **Gallery Image Picker** with cropping support
- ✅ **Camera Launcher** with cropping support  
- ✅ **Image Compression** utility
- ✅ **Combined Functions** for pick/capture + compress in one step
- ✅ **Permission Handling** for both camera and gallery access
- ✅ **File Size Validation** (max 5MB)
- ✅ **TypeScript Support** with proper interfaces

## Available Functions

### 1. `pickImageFromGalleryWithCrop(options?)`
Opens the device gallery to select and crop an image.

```typescript
import { pickImageFromGalleryWithCrop } from '../utils/commonActions';

const image = await pickImageFromGalleryWithCrop({
    width: 800,
    height: 800,
    cropping: true,
    cropperCircleOverlay: false,
    compressImageQuality: 0.8,
});
```

### 2. `launchCameraWithCrop(options?)`
Launches the device camera to capture and crop an image.

```typescript
import { launchCameraWithCrop } from '../utils/commonActions';

const image = await launchCameraWithCrop({
    width: 1024,
    height: 1024,
    cropping: true,
    cropperCircleOverlay: true, // For circular profile photos
    compressImageQuality: 0.9,
});
```

### 3. `compressImage(imageUri, options?)`
Compresses an existing image.

```typescript
import { compressImage } from '../utils/commonActions';

const compressedUri = await compressImage(originalImageUri, {
    compressionMethod: 'manual',
    maxWidth: 1280,
    quality: 0.7,
});
```

### 4. `pickAndCompressImageFromGallery(pickerOptions?, compressionOptions?)`
Picks an image from gallery and compresses it in one step.

```typescript
import { pickAndCompressImageFromGallery } from '../utils/commonActions';

const result = await pickAndCompressImageFromGallery(
    { width: 800, height: 800, cropping: true },
    { maxWidth: 1280, quality: 0.7 }
);

console.log(result.originalImage); // Original image data
console.log(result.compressedUri); // Compressed image URI
```

### 5. `launchCameraAndCompressImage(pickerOptions?, compressionOptions?)`
Launches camera and compresses the captured image in one step.

```typescript
import { launchCameraAndCompressImage } from '../utils/commonActions';

const result = await launchCameraAndCompressImage(
    { width: 1024, height: 1024, cropping: true },
    { maxWidth: 1280, quality: 0.8 }
);
```

## Options

### ImagePickerOptions
```typescript
interface ImagePickerOptions {
    width?: number;                    // Default: 800
    height?: number;                   // Default: 800
    cropping?: boolean;                // Default: true
    cropperCircleOverlay?: boolean;    // Default: false
    mediaType?: 'photo' | 'video' | 'any'; // Default: 'photo'
    freeStyleCropEnabled?: boolean;    // Default: false
    cropperToolbarTitle?: string;      // Default: 'Crop Photo'
    cropperCancelText?: string;        // Default: 'Cancel'
    cropperChooseText?: string;        // Default: 'Choose'
    includeBase64?: boolean;           // Default: false
    compressImageMaxWidth?: number;    // Default: 1024
    compressImageMaxHeight?: number;   // Default: 1024
    compressImageQuality?: number;     // Default: 0.8
}
```

### CompressionOptions
```typescript
interface CompressionOptions {
    compressionMethod?: 'auto' | 'manual'; // Default: 'manual'
    maxWidth?: number;                      // Default: 1280
    maxHeight?: number;                     // Default: undefined
    quality?: number;                       // Default: 0.8
}
```

## Common Use Cases

### Profile Photo (Circular Crop)
```typescript
const profileImage = await pickAndCompressImageFromGallery(
    {
        width: 400,
        height: 400,
        cropping: true,
        cropperCircleOverlay: true,
        cropperToolbarTitle: 'Crop Profile Photo',
    },
    {
        maxWidth: 800,
        quality: 0.8,
    }
);
```

### Post Image (No Crop, High Quality)
```typescript
const postImage = await pickAndCompressImageFromGallery(
    {
        width: 1024,
        height: 1024,
        cropping: false,
        compressImageQuality: 0.9,
    },
    {
        maxWidth: 1920,
        quality: 0.85,
    }
);
```

### Camera Capture
```typescript
const cameraImage = await launchCameraAndCompressImage(
    {
        width: 1024,
        height: 1024,
        cropping: true,
    },
    {
        maxWidth: 1280,
        quality: 0.8,
    }
);
```

## Error Handling

All functions throw errors that should be handled:

```typescript
try {
    const image = await pickImageFromGalleryWithCrop();
    // Use image...
} catch (error) {
    if (error.message === 'Gallery permission not granted') {
        // Handle permission error
    } else if (error.message === 'Image size should not exceed 5MB') {
        // Handle file size error
    } else {
        // Handle other errors
    }
}
```

## Permissions

The utilities automatically handle permissions:
- **Gallery**: Requests photo library access
- **Camera**: Requests camera access
- **Android**: Uses `READ_MEDIA_IMAGES` and `CAMERA` permissions
- **iOS**: Uses `PHOTO_LIBRARY` and `CAMERA` permissions

## File Size Validation

All functions automatically validate that selected/captured images don't exceed 5MB. If they do, an alert is shown and an error is thrown.
