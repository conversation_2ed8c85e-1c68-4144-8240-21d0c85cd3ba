import React, { useEffect } from 'react';
import { Linking } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import dynamicLinks from '@react-native-firebase/dynamic-links';

import LandingScreen from '../screens/LandingScreen';
import LoginScreen from '../screens/auth/login';
import ResetPasswordScreen from '../screens/ResetPasswordScreen';
import CreateAccountScreen from '../screens/register/CreateAccountScreen';
import AccountVerificationScreen from '../screens/register/AccountVerificationScreen';
import PersonalProfile from '../screens/register/PersonalProfileScreen';
import AuthProvider from '../context/AuthContext';
import ClubInformationScreen from '../forms/register/ClubInformationForm';
import GolfProfileScreen from '../screens/register/GolfProfileScreen';
import ConfirmProfileScreen from '../screens/register/ConfirmProfileScreen';
import RegistrationCompleteScreen from '../screens/register/RegistrationCompleteScreen';
import RegistrationScreen from '../screens/register';
import PersonalProfileForm from '../forms/register/PersonalProfileForm';
import GolferProfileForm from '../forms/register/GolferProfileForm';
import AccountSettingsScreen from '../screens/profile/AccountSettingsScreen';
import CreateAccountForm from '../forms/register/CreateAccountForm';
import EditClubInformationForm from '../forms/register/EditClubInformationForm';
import SignUpBlocker from '../screens/auth/signupBlocker/view/SignUpBlocker';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import DeleteChannelConfirmationPopup from '../screens/my-TG-Stream-Chat/view/DeleteChannelConfirmationPopup';
import { GlobalProvider } from '../context/contextApi';

const Stack = createNativeStackNavigator();

export default function AuthStack({ user, refreshUser, token, navigationRef }) {

    const initialRoute = user
        ? user?.deleted_at
            ? 'Landing'
            : user?.registration_complete
            ? 'Registration Complete'
            : user?.verified
            ? user?.signup_current_step === 2
                ? 'Personal Profile Form'
                : user?.signup_current_step === 3
                ? 'Golfer Profile Form'
                : user?.signup_current_step === 4
                ? 'Club Information Form'
                : user?.signup_current_step === 5
                ? 'Confirm Profile'
                : user?.signup_current_step === 6
                ? 'Registration Complete'
                : 'Create Account Form'
            : 'Personal Profile Form'
        : 'Landing';
    console.log('ghfghf', initialRoute);
    return (
        <AuthProvider user={{ user, refreshUser, token }}>
            <GlobalProvider>
                <Stack.Navigator
                    swipeEnabled={false}
                    initialRouteName={initialRoute}
                    screenOptions={{ headerShown: false, headerTintColor: 'black' }}>
                    <Stack.Screen
                        name="Landing"
                        component={LandingScreen}
                        options={{
                            gestureEnabled: false,
                        }}
                    />
                    <Stack.Screen name="Login" component={LoginScreen} />

                    <Stack.Screen name="Reset Password" component={ResetPasswordScreen} />

                    <Stack.Screen name="Create Account" component={CreateAccountScreen} />
                    <Stack.Screen name="Account Settings" component={AccountSettingsScreen} />
                    <Stack.Screen name="Verify Account" component={AccountVerificationScreen} />
                    <Stack.Screen name="RegisterPersonalProfile" component={PersonalProfile} />
                    <Stack.Screen name="RegisterProfile" component={RegistrationScreen} />
                    <Stack.Screen name="Club Information" component={ClubInformationScreen} />
                    <Stack.Screen name="Golfer Profile" component={GolfProfileScreen} />
                    <Stack.Screen name="Confirm Profile" component={ConfirmProfileScreen} />
                    <Stack.Screen name="Registration Complete" component={RegistrationCompleteScreen} />
                    <Stack.Screen name="Personal Profile Form" component={PersonalProfileForm} />
                    <Stack.Screen name="Golfer Profile Form" component={GolferProfileForm} />

                    <Stack.Screen name="Create Account Form" component={CreateAccountForm} />

                    <Stack.Screen name="Club Information Form" component={ClubInformationScreen} />

                    <Stack.Screen name="Edit Club Information Form" component={EditClubInformationForm} />
                    <Stack.Screen name="SignUpBlocker" component={SignUpBlocker} />
                    <Stack.Screen
                        name="DeleteChannelConfirmationPopup"
                        component={DeleteChannelConfirmationPopup}
                        options={{
                            presentation: 'transparentModal',
                            animationTypeForReplace: 'push', // or 'pop'
                            animation: 'fade',
                        }}
                    />
                </Stack.Navigator>
            </GlobalProvider>
        </AuthProvider>
    );
}
