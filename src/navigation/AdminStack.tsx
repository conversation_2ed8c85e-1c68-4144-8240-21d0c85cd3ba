import { createNativeStackNavigator, NativeStackNavigationProp } from '@react-navigation/native-stack';

import AdminLogin from '../screens/admin/AdminLogin';
import { RootStackParamList } from '../interface/type';
import { User } from '../interface';
import AuthProvider from '../context/AuthContext';
import { GlobalProvider } from '../context/contextApi';

const Stack = createNativeStackNavigator();

const AdminStack = ({
    navigation,
    user,
    token,
    refreshUser,
}: {
    navigation: NativeStackNavigationProp<RootStackParamList>;
    user: User;
    token: string;
    refreshUser: () => void;
}) => {
    return (
        <AuthProvider user={{ user, token, refreshUser }} token={token}>
            <GlobalProvider>
                <Stack.Navigator>
                    <Stack.Screen name="AdminLogin" component={AdminLogin} />
                </Stack.Navigator>
            </GlobalProvider>
        </AuthProvider>
    );
};

export default AdminStack;
