import React, { useEffect, useRef, useState } from 'react';
import { getVersion } from 'react-native-device-info';
import Config from 'react-native-config';

import useAuth from '../hooks/useAuth';
import AuthStack from './AuthStack';
import CompleteProfileStack from './CompleteProfileStack';
import MainStack from './MainStack';
import OnboardingStack from './OnboardingStack';
import { useSubscription } from 'react-apollo';
import { GET_USER_SUBS } from '../graphql/queries/user';
import SplashScreen from '../screens/splash/SplashScreen';
import { fetcher } from '../service/fetcher';
import { CHECK_DUES, versionCheckURL } from '../service/EndPoint';
import { Alert, AppState, Linking, Platform } from 'react-native';
import MaintenanceScreen from '../screens/auth/signup/maintenanceScreen/view/MaintenanceScreen';
import UnMuteAccountPopup from '../screens/unmuteAccountPopup/view/UnMuteAccountPopup';
import AdminStack from './AdminStack';

export default function RenderNavigation({
    navigationRef,
    duesState1,
    shareSheetImageUrl,
    user,
    token,
    initializing,
    refreshUser,
    refreshToken,
    needsAnnualConfirmation,
    deferAnnualConfirmation,
    setDeferAnnualConfirmation,
    streamClient,
    maintenance,
    clubValidationRequired,
}) {
    const appState = useRef(AppState.currentState);

    const { data: userData } = useSubscription(GET_USER_SUBS, {
        shouldResubscribe: true,
        variables: {
            user_id: user?.id,
        },
    });

    useEffect(() => {
        refreshUser();
    }, [userData]);

    // This useEffect is use to check force update and show force update popup
    useEffect(() => {
        const subscribeAppState = AppState.addEventListener('change', (nextAppState) => {
            console.log('nextAppState:', nextAppState);
            if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
                console.log('App has come to the foreground!');
                checkAppVersion();
            }
            appState.current = nextAppState;
        });

        checkAppVersion(); // Initial check when component mounts

        return () => {
            subscribeAppState.remove(); // Cleanup function
        };
    }, []);

    function checkAppVersion() {
        console.log('checkAppVersion calling');
        let app_version = getVersion();
        fetcher({
            endpoint: versionCheckURL,
            method: 'POST',
            body: { app_version, device_type: Platform.OS },
        }).then((res) => {
            //Please, update app to new version to enjoy our newest feature!
            if (res?.status === 1) {
                if (res?.force === 1) {
                    const appURL =
                        Platform.OS === 'android'
                            ? 'https://play.google.com/store/apps/details?id=com.thousandgreens'
                            : 'https://apps.apple.com/us/app/thousand-greens/id1536422684';
                    Alert.alert(
                        'Update your app',
                        res?.message,
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    Linking.openURL(appURL);
                                },
                            },
                            res?.force == 0 && {
                                text: 'Cancel',
                                onPress: () => console.log('press cancel'),
                                style: 'cancel',
                            },
                        ],
                        { cancelable: false },
                    );
                }
            }
        });
    }

    if (initializing || maintenance === null) return <SplashScreen />;
    else if (user?.id === Config.ADMIN_ID) {
        return <AdminStack navigation={navigationRef} user={user} token={token} refreshUser={refreshUser} />;
    } else if (!initializing && maintenance === 1) {
        return <MaintenanceScreen />;
    } else if (user && user.is_legacy_user) {
        return <OnboardingStack user={user} token={token} refreshUser={refreshUser} />;
    } else if (user && user.account_activated) {
        if (user?.profile_complete) {
            if (duesState1) {
                return (
                    <>
                        <MainStack
                            user={user}
                            token={token}
                            refreshUser={refreshUser}
                            refreshToken={refreshToken}
                            navigationRef={navigationRef}
                            needsAnnualConfirmation={needsAnnualConfirmation}
                            deferAnnualConfirmation={deferAnnualConfirmation}
                            setDeferAnnualConfirmation={setDeferAnnualConfirmation}
                            duesState={duesState1}
                            client={streamClient}
                            shareSheetImageUrl={shareSheetImageUrl}
                            clubValidationRequired={clubValidationRequired}
                        />
                    </>
                );
            } else return <SplashScreen />;
        } else {
            return <CompleteProfileStack user={user} token={token} refreshUser={refreshUser} />;
        }
    } else {
        return <AuthStack user={user} refreshUser={refreshUser} token={token} navigationRef={navigationRef} />;
    }
}
