import { Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useContext } from 'react';

// Context imports
import { FormContext } from '../FormContext';

// Theme, utils and assets imports
import { colors } from '../../theme/theme';
import { Back } from '../../assets/images/svg';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';

const FilterHeader = ({ onBackPress = () => {}, defaultFilter = {}, handleDefaultFilter = (b: boolean) => {} }) => {
    const { height } = Dimensions.get('window');
    const { updateFields, form } = useContext(FormContext);

    function areObjectsEqual(obj1: any, obj2: any) {
        const sortKeys = (obj: any) => JSON.stringify(obj, Object.keys(obj).sort());
        return sortKeys(obj1) === sortKeys(obj2);
    }

    return (
        <View
            style={{
                flexDirection: 'row',
                paddingHorizontal: 15,
                shadowColor: colors.shadowColor1,
                shadowOffset: { width: -2, height: 4 },
                shadowOpacity: 0.2,
                elevation: 13,
                shadowRadius: 3,
                height: Size.SIZE_50,
                backgroundColor: colors.white,
            }}>
            <View
                style={{
                    flex: 1,
                    alignItems: 'center',
                    flexDirection: 'row',
                }}>
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                    }}>
                    <Text style={styles.titleStyle}>Filters</Text>
                    {!areObjectsEqual(form, defaultFilter) && (
                        <TouchableOpacity
                            onPress={() => {
                                handleDefaultFilter(true);
                                updateFields(defaultFilter);
                            }}>
                            <Text
                                style={[
                                    {
                                        color: colors.lightBlack,
                                        fontSize: Typography.FONT_SIZE_14,
                                        fontFamily: 'Ubuntu-Medium',
                                        fontWeight: '500',
                                    },
                                ]}>
                                Clear All
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
                <TouchableOpacity
                    onPress={onBackPress}
                    style={{
                        position: 'absolute',
                        paddingVertical: height * 0.015,
                        alignItems: 'center',
                    }}>
                    <Back width={18} height={18} />
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default FilterHeader;

const styles = StyleSheet.create({
    titleStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        flex: 1,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        marginLeft: Spacing.SCALE_26,
    },
});
