import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React, { useContext, useMemo } from 'react';
import FastImage from 'react-native-fast-image';

import { DatePickerSvgIcon, MailIcon, PhoneIcon, SearchIconNew } from '../../../assets/svg';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { RESULT_NOT_FOUND_TEXT } from '../../../utils/constants/strings';
import { Club, PeopleModal, SearchedUser, UserClub } from '../../../interface';
import SearchInput from './SearchInput';
import PeopleDropDown from './PeopleDropDown';
import { handleTimeFormat } from '../../../components/timeFormatComponent/handleTimeFormat';
import { FormContext } from '../../../forms/FormContext';
import { AuthContext } from '../../../context/AuthContext';
interface SearchedResultComponentProps {
    searchedUser: SearchedUser | null;
    isResultFound: boolean;
    email: string;
    dropdownVisible: boolean;
    setDropdownVisible: (visible: boolean) => void;
    setDateRangeModal: (modal: any) => void;
    dateRangeModal: any;
    peopleModal: PeopleModal;
    setPeopleModal: (modal: PeopleModal) => void;
}

const SearchedResultComponent = ({
    searchedUser,
    isResultFound,
    email,
    dropdownVisible,
    setDropdownVisible,
    setDateRangeModal,
    dateRangeModal,
    peopleModal,
    setPeopleModal,
}: SearchedResultComponentProps) => {
    const { form } = useContext(FormContext);
    const { user } = useContext(AuthContext);

    const filteredClubs = useMemo(() => {
        return searchedUser?.clubs?.filter((club: Club) => !user?.clubs.some((c: UserClub) => c.club_id === club.id));
    }, [searchedUser, user]);

    if (!isResultFound) {
        return (
            <View style={styles.noResultContainer}>
                <SearchIconNew height={Size.SIZE_50} width={Size.SIZE_50} />
                <Text style={styles.noResultText}>No Result Found</Text>
                <Text style={styles.noResultBodyText}>{RESULT_NOT_FOUND_TEXT}</Text>
            </View>
        );
    }

    return (
        <View style={styles.dataFoundContainer}>
            <Text style={styles.title}>{email ? 'Email' : 'Mobile Number'}</Text>
            <Text style={styles.emailText}>{email ? searchedUser?.email : searchedUser?.phone}</Text>
            <Text style={[styles.title, { marginTop: Spacing.SCALE_24 }]}>We found a match</Text>
            <View style={styles.matchContainer}>
                <View style={styles.profileImage}>
                    {searchedUser?.profilePhoto ? (
                        <FastImage
                            source={{ uri: searchedUser?.profilePhoto }}
                            style={styles.profileImage}
                            resizeMode={FastImage.resizeMode.contain}
                        />
                    ) : (
                        <View style={styles.profileImage}>
                            <Text style={styles.profileText}>{searchedUser?.first_name[0]}</Text>
                        </View>
                    )}
                </View>
                <View style={{ width: '70%' }}>
                    <Text numberOfLines={1} ellipsizeMode="tail" style={styles.nameText}>
                        {searchedUser?.first_name} {searchedUser?.last_name}
                    </Text>
                    <View style={styles.emailContainer}>
                        <MailIcon />
                        <Text style={[styles.email, { marginVertical: Spacing.SCALE_8 }]}>{searchedUser?.email}</Text>
                    </View>
                    <View style={styles.emailContainer}>
                        <PhoneIcon />
                        <Text style={styles.email}>{searchedUser?.phone}</Text>
                    </View>
                </View>
            </View>
            <SearchInput
                dropdownVisible={dropdownVisible}
                setDropdownVisible={setDropdownVisible}
                clubs={filteredClubs || []}
            />
            <Text style={styles.label}>Game Date</Text>
            <TouchableOpacity
                style={styles.inputWrapper}
                onPress={() => setDateRangeModal({ ...dateRangeModal, visible: true })}>
                <Text style={[styles.input, { color: form.date ? colors.lightBlack : colors.darkgray }]}>
                    {form.date ? handleTimeFormat(form.date) : 'Select'}
                </Text>
                <View style={{ padding: Spacing.SCALE_10 }}>
                    <DatePickerSvgIcon />
                </View>
            </TouchableOpacity>
            <PeopleDropDown peopleModal={peopleModal} setPeopleModal={setPeopleModal} />
        </View>
    );
};

export default SearchedResultComponent;

const styles = StyleSheet.create({
    noResultContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: Spacing.SCALE_24,
        marginBottom: Spacing.SCALE_32,
        marginHorizontal: Spacing.SCALE_16,
    },
    noResultText: {
        color: colors.dark_charcoal,
        fontSize: Size.SIZE_18,
        fontFamily: 'Ubuntu-Medium',
        marginTop: Spacing.SCALE_10,
    },
    noResultBodyText: {
        color: colors.greyRgb,
        fontSize: Size.SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_6,
        lineHeight: Size.SIZE_18,
        textAlign: 'center',
        fontWeight: '400',
        paddingHorizontal: Spacing.SCALE_5,
    },
    title: {
        color: colors.darkgray,
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
    },
    emailText: {
        color: colors.dark_charcoal,
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_6,
    },
    dataFoundContainer: {
        flex: 1,
        marginBottom: Spacing.SCALE_24,
    },
    matchContainer: {
        backgroundColor: colors.greyRgba,
        padding: Spacing.SCALE_22,
        borderRadius: Size.SIZE_8,
        marginTop: Spacing.SCALE_9,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_13,
    },
    profileContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    profileImage: {
        width: Size.SIZE_67,
        height: Size.SIZE_67,
        borderRadius: Size.SIZE_4,
        backgroundColor: colors.whiteRGB,
        justifyContent: 'center',
        alignItems: 'center',
    },
    nameText: {
        color: colors.dark_charcoal,
        fontSize: Size.SIZE_18,
        fontFamily: 'Ubuntu-Medium',
        flex: 1,
    },
    email: {
        color: colors.dark_charcoal,
        fontSize: Size.SIZE_14,
        fontFamily: 'Ubuntu-Regular',
    },
    emailContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
    },
    profileText: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_28,
        color: 'rgba(9, 128, 137, 1)',
        fontFamily: 'Ubuntu-Medium',
    },
    fieldLabelStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_14,
        fontWeight: '400',
        color: colors.darkGreyRgba,
    },
    label: {
        color: colors.systemMessageText,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_24,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
    },
    inputWrapper: {
        borderBottomWidth: 1,
        borderBottomColor: colors.borderGray,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    clubContainer: {
        marginTop: Spacing.SCALE_10,
        backgroundColor: colors.whiteColor,
        padding: Spacing.SCALE_20,
        borderRadius: Size.SIZE_8,
        position: 'absolute',
        top: 38,
        left: 0,
        right: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: 4,
        maxHeight: Size.SIZE_120,
    },
    clubText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        marginBottom: Spacing.SCALE_18,
    },
});
