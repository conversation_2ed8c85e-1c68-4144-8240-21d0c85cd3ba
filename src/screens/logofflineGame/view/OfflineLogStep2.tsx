import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React, { useContext, useState } from 'react';
import { GlobalContext } from '../../../context/contextApi';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { RequesterIcon, HostIconBlack, RequesterIconWhite, HostIconWhite } from '../../../assets/svg';
import CancelButton from '../../../components/buttons/CancelButton';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import { LOG_SCREEN_BODY, LOG_SCREEN_HEADER } from '../../../utils/constants/strings';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import config from '../../../config';

const OfflineLogSecondStepScreen = () => {
    const { actions } = useContext(GlobalContext);
    const [selectedRole, setSelectedRole] = useState<string | null>(null);
    const navigation = useNavigation<NativeStackNavigationProp<any>>();

    const handleRoleSelection = (role: string) => {
        setSelectedRole(role);
    };

    const handleDismiss = () => {
        // Add a small delay to let the fade animation of the modal complete
        setTimeout(() => {
            actions.setOfflineLogGameStep(0);
        }, 100);
    };

    const handleContinue = () => {
        if (selectedRole) {
            // Navigate to next step with the selected role
            navigation.navigate(config.routes.LOG_PLAYER_GAME_DETAILS, {role: selectedRole});
            actions.setOfflineLogGameStep(0);
        }
    };

    return (
        <View style={styles.container}>
            <Text style={styles.headerText}>{LOG_SCREEN_HEADER}</Text>
            <Text style={styles.subHeaderText}>{LOG_SCREEN_BODY}</Text>
            <Text style={styles.questionText}>Were you a requester or host?</Text>
            <View style={styles.optionsContainer}>
                <TouchableOpacity onPress={() => handleRoleSelection('requester')}>
                    <View
                        style={[
                            styles.iconContainer,
                            { backgroundColor: selectedRole === 'requester' ? colors.tealRgb : colors.lightGrey },
                        ]}>
                        {selectedRole === 'requester' ? (
                            <RequesterIconWhite width={36} height={36} />
                        ) : (
                            <RequesterIcon width={36} height={36} />
                        )}
                    </View>
                    <Text style={styles.optionText}>Requester</Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => handleRoleSelection('host')}>
                    <View
                        style={[
                            styles.iconContainer,
                            { backgroundColor: selectedRole === 'host' ? colors.tealRgb : colors.lightGrey },
                        ]}>
                        {selectedRole === 'host' ? (
                            <HostIconWhite width={36} height={36} />
                        ) : (
                            <HostIconBlack width={36} height={36} />
                        )}
                    </View>
                    <Text style={styles.optionText}>Host</Text>
                </TouchableOpacity>
            </View>

            <View style={styles.buttonContainer}>
                <CancelButton
                    text="Dismiss"
                    onPress={handleDismiss}
                    width="48%"
                    customStyle={styles.cancelButtonStyle}
                    textStyle={styles.cancelButtonText}
                />

                <TealButtonNew
                    text="Continue"
                    onPress={handleContinue}
                    disabled={!selectedRole}
                    btnStyle={selectedRole ? styles.tealButtonStyle : { backgroundColor: colors.dustyGrey }}
                    textStyle={styles.tealButtonText}
                    loading={false}
                    disabledStyle={null}
                />
            </View>
        </View>
    );
};

export default OfflineLogSecondStepScreen;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.white,
        borderTopLeftRadius: Size.SIZE_20,
        borderTopRightRadius: Size.SIZE_20,
        padding: Spacing.SCALE_16,
        alignItems: 'center',
    },
    headerText: {
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '500',
        color: colors.darkCharcoal,
        marginBottom: Spacing.SCALE_12,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_32,
        marginTop: Spacing.SCALE_8,
    },
    subHeaderText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.greyRgb,
        textAlign: 'center',
        lineHeight: Size.SIZE_21,
        marginBottom: Spacing.SCALE_15,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    questionText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.dark_charcoal,
        marginBottom: Spacing.SCALE_16,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Regular',
    },
    optionsContainer: {
        flexDirection: 'row',
        width: '100%',
        marginBottom: Spacing.SCALE_20,
        justifyContent: 'center',
        columnGap: Spacing.SCALE_60,
    },
    optionButton: {
        alignItems: 'center',
        padding: Spacing.SCALE_16,
        borderRadius: Size.SIZE_20,
        width: '40%',
    },
    selectedOption: {
        backgroundColor: colors.tealRGBAColor,
        borderWidth: 1,
        borderColor: colors.tealRgb || '#098089',
    },
    iconContainer: {
        width: Size.SIZE_70,
        height: Size.SIZE_70,
        borderRadius: Size.SIZE_74,
        backgroundColor: colors.lightGrey,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_4,
        position: 'relative',
    },
    optionText: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.darkCharcoal,
        fontFamily: 'Ubuntu-Regular',
        textAlign: 'center',
        fontWeight: '400',
        lineHeight: Size.SIZE_32,
    },
    badge: {
        position: 'absolute',
        bottom: -5,
        right: -5,
        backgroundColor: colors.tealRgb || '#098089',
        paddingHorizontal: Spacing.SCALE_6,
        paddingVertical: Spacing.SCALE_2,
        borderRadius: Size.SIZE_4,
        flexDirection: 'row',
        alignItems: 'center',
    },
    badgeText: {
        color: colors.white,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: 'bold',
        fontFamily: 'Ubuntu-Bold',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    },
    cancelButtonStyle: {
        backgroundColor: colors.greyRgba,
        paddingVertical: Spacing.SCALE_16,
        borderRadius: Size.SIZE_10,
        height: Size.SIZE_50,
        alignItems: 'center',
    },
    tealButtonStyle: {
        backgroundColor: colors.tealRgb || '#098089',
        paddingVertical: Spacing.SCALE_16,
        borderRadius: Size.SIZE_10,
        width: '50%',
        height: Size.SIZE_50,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: colors.dark_charcoal,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
    tealButtonText: {
        color: colors.whiteRGB,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
});
