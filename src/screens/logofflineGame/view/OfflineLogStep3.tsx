import { KeyboardAvoidingView, Platform, StyleSheet, Text, TextInput, View, ScrollView } from 'react-native';
import React, { useContext, useState } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
// @ts-ignore
import * as yup from 'yup';

import { Typography } from '../../../utils/responsiveUI';
import { Spacing } from '../../../utils/responsiveUI';
import { Size } from '../../../utils/responsiveUI';
import MyFriendPhoneNumberInput from '../../../components/fields/MyFriendPhoneNumberInput';
import { colors } from '../../../theme/theme';
import { GolfClubTeal } from '../../../assets/svg';
import { LOG_SCREEN_BODY_2, SUCCESS } from '../../../utils/constants/strings';
import CancelButton from '../../../components/buttons/CancelButton';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import SearchedResultComponent from './SearchedResultComponent';
import Form from '../../../forms/FormContext';
import { handleLogOfflineGame, handleSearchUser } from '../action/searchUser';
import { AuthContext } from '../../../context/AuthContext';
import CalenderComponent from './CalenderComponent';
import { Club, DateRangeModal, PeopleModal, SearchedUser, UserClub } from '../../../interface';
import showToast from '../../../components/toast/CustomToast';
import { EMAIL_VALIDATION } from '../../../utils/constants/validationConstants';

type RootStackParamList = {
    LogPlayerGameDetails: {
        role: string;
    };
    LogFinalPopup: undefined;
};

interface LogPlayerGameDetailsProps {
    navigation: NativeStackNavigationProp<RootStackParamList>;
    route: RouteProp<RootStackParamList, 'LogPlayerGameDetails'>;
}

interface SearchUserPayload {
    userId: string;
    userType: string;
    email?: string;
    phoneNumber?: string;
}

interface RequestFormType {
    club?: any;
    date?: string;
    number_of_players?: string;
}

const LogPlayerGameDetails = ({ navigation, route }: LogPlayerGameDetailsProps) => {
    const { user } = useContext(AuthContext);
    const { role } = route.params;
    const [email, setEmail] = useState('');
    const [phoneNumber, setPhoneNumber] = useState({
        countryCode: 'US',
        dialCode: '+1',
        name: 'United States',
        phoneNumber: '',
        unmaskedPhoneNumber: '',
    });
    const [isEmailMode, setIsEmailMode] = useState(false);
    const [errors, setErrors] = useState('');
    const [phoneError, setPhoneError] = useState('');
    const [emailError, setEmailError] = useState('');
    const [loading, setLoading] = useState(false);
    const [setPhoneNumberLength, setSetPhoneNumberLength] = useState('');
    const [countryCode, setCountryCode] = useState('');
    const [requestForm, setRequestForm] = useState<RequestFormType>({});
    const [searchedUser, setSearchedUser] = useState<SearchedUser | null>(null);
    const [isResultFound, setIsResultFound] = useState(false);
    const [isSearchPerformed, setIsSearchPerformed] = useState(false);
    const [dropdownVisible, setDropdownVisible] = useState(false);
    const [dateRangeModal, setDateRangeModal] = useState<DateRangeModal>({
        visible: false,
        date: '',
    });
    const [peopleModal, setPeopleModal] = useState<PeopleModal>({
        visible: false,
        number_of_players: '1',
    });

    const isFormValid = () => {
        if (!isSearchPerformed || !isResultFound) return true;
        return !!(requestForm?.club && requestForm?.date && requestForm?.number_of_players);
    };

    const handleSearchBtn = async () => {
        if (email && !EMAIL_VALIDATION.test(email)) {
            setEmailError('Please enter a valid email');
        } else if (phoneNumber.phoneNumber && phoneNumber.phoneNumber.length === 0) {
            setPhoneError('Please enter a valid phone number');
        } else {
            setLoading(true);
            let payload: SearchUserPayload = {
                userId: user?.id,
                userType: role,
            };
            if (email) {
                payload = { ...payload, email: email };
            } else {
                payload = { ...payload, phoneNumber: phoneNumber?.dialCode + phoneNumber?.unmaskedPhoneNumber };
            }
            let res = await handleSearchUser(payload);
            setIsSearchPerformed(true);
            if (res?.status) {
                if (res?.data?.length) {
                    setIsResultFound(true);
                    setSearchedUser(res.data[0]); // Assuming we want to show the first match
                } else {
                    setIsResultFound(false);
                    setSearchedUser(null);
                }
            }
        }
        setLoading(false);
    };

    const onSubmit = async () => {
        setLoading(true);
        let payload = {
            requesterId: role === 'host' ? searchedUser?.id : user?.id,
            hostId: role === 'host' ? user?.id : searchedUser?.id,
            clubId: requestForm?.club?.id,
            gameDate: requestForm?.date,
            numberOfPlayers: requestForm?.number_of_players,
            userId: user?.id,
        };
        let res = await handleLogOfflineGame(payload);
        if (res?.status) {
            showToast({ message: res?.message, type: SUCCESS, header: '' });
            navigation.goBack();
            navigation.navigate('LogFinalPopup');
        }
        setLoading(false);
    };

    const getInitialValues = () => {
        return {
            club: '',
            number_of_players: '1',
            date: '',
        };
    };

    const handlePhoneNumberChange = (data: any) => {
        setPhoneNumber(data);
        if (data.phoneNumber) {
            setIsEmailMode(false);
        }
    };

    const handleEmailChange = (data: string) => {
        if (data != ' ') {
            setEmailError('');
            setEmail(data);
            setIsEmailMode(data.length > 0);
        }
    };

    return (
        <>
            <Form
                onSubmit={(form: any) => {
                    onSubmit();
                }}
                onUpdate={(form: any) => {
                    setRequestForm(form);
                }}
                customErrors={{}}
                validationSchema={{
                    club: yup.object().required('Club is required'),
                    number_of_players: yup.string().required('Number of players is required'),
                    date: yup.string().required('Date range is required'),
                }}
                initialValues={getInitialValues()}>
                <View style={{ flex: 1, backgroundColor: colors.transparentRgba }}>
                    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{ flex: 1 }}>
                        <ScrollView
                            contentContainerStyle={{ flexGrow: 1 }}
                            keyboardShouldPersistTaps="handled"
                            // scrollEnabled={false}
                            nestedScrollEnabled
                            bounces={false}>
                            <View style={styles.container}>
                                <View style={styles.golfClubTealWrapper}>
                                    <GolfClubTeal />
                                </View>
                                <Text style={styles.titleText}>Log a played game</Text>
                                <Text style={styles.subTitleText}>{LOG_SCREEN_BODY_2}</Text>
                                {!searchedUser && (
                                    <>
                                        <Text style={styles.textInputTitle}>
                                            Enter {role === 'host' ? 'requester' : 'host'}'s contact information
                                        </Text>
                                        <MyFriendPhoneNumberInput
                                            title=""
                                            name="Mobile Number"
                                            valueState={[phoneNumber, handlePhoneNumberChange]}
                                            errors={errors}
                                            setErrors={setErrors}
                                            setPhoneError={setPhoneError}
                                            countryCode={countryCode}
                                            setPhoneNumberLength={setPhoneNumberLength}
                                            containerStyle={{}}
                                            titleStyle={{}}
                                            phoneInputStyle={styles.phoneInputStyle}
                                            dialCodeTextStyle={{}}
                                            testVal=""
                                            newCountryCode=""
                                            placeholder="Mobile Number"
                                            placeholderTextColor={
                                                isEmailMode ? colors.disableGreyColor : colors.greyRgb
                                            }
                                            editable={isEmailMode ? false : true}
                                        />
                                        {phoneError?.length > 0 && (
                                            <Text style={styles.errorTextStyle}>{phoneError}</Text>
                                        )}
                                        <Text style={styles.orText}>or</Text>
                                        <View style={{ marginBottom: Spacing.SCALE_32 }}>
                                            <TextInput
                                                placeholderTextColor={
                                                    phoneNumber.phoneNumber ? colors.disableGreyColor : colors.greyRgb
                                                }
                                                style={[
                                                    styles.inputStyle,
                                                    phoneNumber.phoneNumber
                                                        ? { backgroundColor: 'rgba(242, 242, 242, 0.4)' }
                                                        : null,
                                                ]}
                                                value={email}
                                                onChangeText={handleEmailChange}
                                                placeholder="Email"
                                                editable={!phoneNumber.phoneNumber}
                                            />
                                            {emailError?.length > 0 && (
                                                <Text style={styles.errorTextStyle}>{emailError}</Text>
                                            )}
                                        </View>
                                    </>
                                )}
                                {isSearchPerformed && (
                                    <SearchedResultComponent
                                        searchedUser={searchedUser}
                                        isResultFound={isResultFound}
                                        email={email}
                                        dropdownVisible={dropdownVisible}
                                        setDropdownVisible={setDropdownVisible}
                                        setDateRangeModal={setDateRangeModal}
                                        dateRangeModal={dateRangeModal}
                                        peopleModal={peopleModal}
                                        setPeopleModal={setPeopleModal}
                                    />
                                )}
                                <View style={styles.btnWrapper}>
                                    <CancelButton
                                        text="Cancel"
                                        onPress={() => {
                                            navigation.goBack();
                                        }}
                                        width="48%"
                                        customStyle={styles.cancelButtonStyle}
                                        textStyle={styles.cancelButtonText}
                                    />
                                    <TealButtonNew
                                        text={isSearchPerformed && isResultFound ? 'Log A Game' : 'Search'}
                                        onPress={isSearchPerformed && isResultFound ? handleSearchBtn : handleSearchBtn}
                                        disabled={
                                            false
                                        }
                                        btnStyle={
                                            (!email && !phoneNumber.phoneNumber) ||
                                            (isSearchPerformed && !isFormValid())
                                                ? { backgroundColor: colors.dustyGrey }
                                                : { backgroundColor: colors.tealRgb }
                                        }
                                        textStyle={styles.tealButtonText}
                                        loading={loading}
                                        disabledStyle={null}
                                    />
                                </View>
                            </View>
                        </ScrollView>
                    </KeyboardAvoidingView>
                </View>
                {dateRangeModal.visible && (
                    <CalenderComponent
                        modal={{
                            ...dateRangeModal,
                        }}
                        setModal={setDateRangeModal}
                    />
                )}
            </Form>
        </>
    );
};

export default LogPlayerGameDetails;

const styles = StyleSheet.create({
    textInputWrapper: {},
    inputStyle: {
        backgroundColor: 'rgba(242, 242, 242, 1)',
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_8,
        paddingLeft: Spacing.SCALE_10,
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_16,
    },
    btnWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    errorTextStyle: {
        color: 'red',
        marginTop: Spacing.SCALE_4,
    },
    golfClubTealWrapper: {
        width: Size.SIZE_60,
        height: Size.SIZE_60,
        alignSelf: 'center',
        backgroundColor: colors.greyRgba,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    titleText: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_24,
        color: colors.dark_charcoal,
        textAlign: 'center',
        marginVertical: Spacing.SCALE_12,
    },
    subTitleText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.greyRgb,
        textAlign: 'center',
        lineHeight: Size.SIZE_21,
        marginBottom: Spacing.SCALE_31,
        paddingHorizontal: Spacing.SCALE_21,
    },
    container: {
        backgroundColor: colors.whiteRGB,
        borderTopLeftRadius: Size.SIZE_8,
        borderTopRightRadius: Size.SIZE_8,
        width: '100%',
        position: 'absolute',
        bottom: 0,
        paddingVertical: Spacing.SCALE_23,
        paddingHorizontal: Spacing.SCALE_16,
    },
    textInputTitle: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        color: colors.dark_charcoal,
        marginBottom: Spacing.SCALE_8,
    },
    orText: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkGreyRgba,
        marginVertical: Spacing.SCALE_6,
        textAlign: 'center',
    },
    cancelButtonStyle: {
        backgroundColor: colors.greyRgba,
        paddingVertical: Spacing.SCALE_16,
        borderRadius: Size.SIZE_10,
        height: Size.SIZE_50,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: colors.dark_charcoal,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
    tealButtonText: {
        color: colors.whiteRGB,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
    phoneInputStyle: {
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
    },
});
