import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext } from 'react';
import FastImage from 'react-native-fast-image';

// Theme, Interfaces and responsive UI imports
import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';
import { GameReview } from '../../../../interface';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../../interface/type';
import { handleTimeFormat } from '../../../../components/timeFormatComponent/handleTimeFormat';
import redirectToUserProfile from '../../../requests/action/openUserProfile';
import { AuthContext } from '../../../../context/AuthContext';
import { GlobalContext } from '../../../../context/contextApi';
import { ClubGolfIconTeal } from '../../../../assets/svg';

// TODO: Added this component in Golfer profile screen due to same UI for both Profile Info and Club Info and handled by comeFrom props

const GameReviewListingCard = ({
    item,
    navigation,
    comeFrom = '',
}: {
    item: GameReview;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    comeFrom?: string;
}) => {
    console.log("item", item);
    const { user } = useContext(AuthContext);
    const { state } = useContext(GlobalContext);
    return (
        <View style={styles.container}>
            <View style={[styles.reviewContainer, !comeFrom && { alignItems: 'center' }]}>
                {comeFrom === 'Profile Info' ? (
                    <ClubGolfIconTeal />
                ) : (
                    <TouchableOpacity
                        style={styles.profileContainer}
                        onPress={() => {
                            redirectToUserProfile({ userId: item.user_id, navigation, user });
                        }}>
                        {item.profile_photo ? (
                            <FastImage
                                source={{ uri: item.profile_photo, priority: FastImage.priority.high }}
                                resizeMode={FastImage.resizeMode.contain}
                                style={styles.profileImage}
                            />
                        ) : (
                            <Text style={styles.profileText}>
                                {state?.allFriendsId[item.user_id]
                                    ? `${item.first_name.charAt(0)}`
                                    : item.username.charAt(0)}
                            </Text>
                        )}
                    </TouchableOpacity>
                )}
                <View style={styles.nameContainer}>
                    <Text style={styles.nameText}>
                        {comeFrom
                            ? item?.club_name
                            : state?.allFriendsId[item.user_id]
                            ? `${item.first_name} ${item.last_name}`
                            : item.username}
                    </Text>
                    <Text style={styles.dateText}>Game Date: {handleTimeFormat(item.game_date)}</Text>
                </View>
            </View>
            {item.photo && (
                <TouchableOpacity
                    style={styles.imageContainer}
                    onPress={() => navigation.navigate('Image Show', { imageUri: item.photo || '' })}>
                    <FastImage
                        source={{ uri: item.photo, priority: FastImage.priority.high }}
                        resizeMode={FastImage.resizeMode.cover}
                        style={styles.image}
                    />
                </TouchableOpacity>
            )}
            <Text style={styles.reviewText}>{item.review.trim()}</Text>
        </View>
    );
};

export default GameReviewListingCard;

const styles = StyleSheet.create({
    container: {
        padding: Spacing.SCALE_12,
        borderWidth: 1,
        borderColor: colors.lightgray,
        borderRadius: Spacing.SCALE_10,
        backgroundColor: colors.whiteRGB,
        marginVertical: Spacing.SCALE_4,
    },
    reviewContainer: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_10,
    },
    profileContainer: {
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.lightgray,
        borderWidth: 1,
        borderColor: colors.lightgray,
        justifyContent: 'center',
        alignItems: 'center',
    },
    profileImage: {
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.lightgray,
        resizeMode: FastImage.resizeMode.cover,
    },
    profileText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
        textTransform: 'capitalize',
    },
    nameText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
    },
    reviewText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Spacing.SCALE_20,
        marginTop: Spacing.SCALE_14,
    },
    dateText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
    },
    nameContainer: {
        rowGap: Spacing.SCALE_4,
    },
    imageContainer: {
        marginTop: Spacing.SCALE_14,
        width: '100%',
        height: Size.SIZE_170,
        borderRadius: Size.SIZE_6,
    },
    image: {
        width: '100%',
        height: Size.SIZE_170,
        borderRadius: Size.SIZE_6,
    },
});
