import { FlatList, Pressable, StyleSheet, Text, View } from 'react-native';
import React, { useContext, useLayoutEffect, useMemo } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useFetchReactions } from 'stream-chat-react-native';
import FastImage from 'react-native-fast-image';

// Utils, Constants, Theme and interfaces
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { RootStackParamList } from '../../../interface/type';
import emojis from '../../../utils/reactions/emoji';
import { colors } from '../../../theme/theme';
import { AuthContext } from '../../../context/AuthContext';
import { GlobalContext } from '../../../context/contextApi';
import RequestScreenSkelton from '../../requests/view/RequestScreenSkelton';

interface ReactionUsersListProps {
    route: {
        params: {
            reactedUsers: string[];
            type: string;
            fetchedReactions: any;
        };
    };
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

const ReactionUsersList = ({ route, navigation }: ReactionUsersListProps) => {
    const { state, actions } = useContext(GlobalContext);
    const { ReactionType: type } = state?.reactionState;
    const { reactions: streamReactions } = useFetchReactions({
        message: state?.reactionState?.message,
        sort: {
            created_at: -1,
        },
    });
    const fetchedReactions = useMemo(() => {
        if (!streamReactions || !Array.isArray(streamReactions)) {
            return [];
        }
        return streamReactions.map((reaction) => ({ ...reaction }));
    }, [streamReactions]);
    const { user } = useContext(AuthContext);
    useLayoutEffect(() => {
        actions.setAppSkeltonLoader(true);
        if (fetchedReactions.length) {
            actions.setAppSkeltonLoader(false);
        }
    }, [fetchedReactions]);

    return (
        <View style={styles.modalContainer}>
            <Pressable
                style={styles.container}
                onPress={() => actions.setReactionState({ message: {}, ReactionType: '', reactionListVisible: false })}
            />
            <View style={styles.bottomOverlay}>
                <View style={styles.reactionLine} />
                {state?.appSkeltonLoader ? (
                    <RequestScreenSkelton screen={'ReactionUsersList'} />
                ) : (
                    <>
                        <View style={styles.reactionContainer}>
                            <Text>{emojis[type as keyof typeof emojis]}</Text>
                            <Text>{fetchedReactions?.filter((reaction: any) => reaction.type === type).length}</Text>
                        </View>
                        <FlatList
                            data={fetchedReactions.filter((reaction: any) => reaction.type === type)}
                            renderItem={({ item }) => (
                                <View style={styles.reactionUserContainer}>
                                    <View style={styles.reactionUserImageContainer}>
                                        {item?.user?.image ? (
                                            <FastImage
                                                source={{ uri: item?.user?.image }}
                                                style={styles.reactionUserImage}
                                            />
                                        ) : (
                                            <Text style={styles.reactionUserImageText}>
                                                {item?.user?.name?.charAt(0)}
                                            </Text>
                                        )}
                                    </View>
                                    <Text style={styles.reactionText}>
                                        {actions.allFriendsId?.includes(item?.user?.id)
                                            ? item?.user?.name
                                            : item?.user?.name?.charAt(0)}
                                        {item?.user?.id === user?.id ? (
                                            <Text style={{ color: colors.greyRgb }}>(you)</Text>
                                        ) : null}
                                    </Text>
                                </View>
                            )}
                        />
                    </>
                )}
            </View>
        </View>
    );
};

export default ReactionUsersList;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    modalContainer: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        minHeight: Size.SIZE_160,
        backgroundColor: 'white',
        maxHeight: Size.SIZE_250,
        borderTopLeftRadius: Size.SIZE_12,
        borderTopRightRadius: Size.SIZE_12,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_10,
    },
    reactionLine: {
        height: 4,
        width: 74,
        borderRadius: 20,
        backgroundColor: colors.greyCheckBox,
        alignSelf: 'center',
        marginBottom: Spacing.SCALE_16,
    },
    reactionContainer: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_12,
        alignItems: 'center',
        marginBottom: Spacing.SCALE_25,
    },
    reactionText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: 16,
        color: colors.dark_charcoal,
    },
    reactionUserContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_12,
        marginBottom: Spacing.SCALE_12,
    },
    reactionUserImageContainer: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_12,
        backgroundColor: colors.greyRgba,
        justifyContent: 'center',
        alignItems: 'center',
    },
    reactionUserImage: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_12,
    },
    reactionUserImageText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_16,
        color: colors.tealRgb,
    },
});
