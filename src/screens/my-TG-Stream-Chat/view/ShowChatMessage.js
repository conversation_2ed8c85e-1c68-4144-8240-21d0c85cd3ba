import React, { useContext, useMemo, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { TouchableOpacity, Text, View, Image, StyleSheet, Pressable, FlatList } from 'react-native';
import moment from 'moment';

import ShowChatImages from './showMessageImages';
import { handleMessageScreenParticipantsName } from './handleMessageScreenParticipantsName';
import { AuthContext } from '../../../context/AuthContext';
import { GlobalContext } from '../../../context/contextApi';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { colors } from '../../../theme/theme';
import { DELETED_USER_ON_TAP_TEXT } from '../client';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import FastImage from 'react-native-fast-image';
import emojis from '../../../utils/reactions/emoji';

const ShowChatMessage = ({ message, alignment, onLongPress, onPressClick, reactions }) => {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const { channel, setChannel, previousChannel, setPreviousChannel } = useContext(StreamChatContext);
    const navigation = useNavigation();
    const styles = makeStyles(alignment);
    const messageTime = moment.utc(message?.created_at).local().format('h:mm A');

    const onClickUserDp = () => {
        setPreviousChannel(channel);
        const selectedUser = message?.user;
        navigation.navigate('UserProfileScreen', {
            selectedUser: { id: selectedUser?.id },
            isComeFromChat: true,
        });
    };

    const deletedMessages = () => {
        return (
            <>
                <Text style={styles.message}>This message was deleted.</Text>
                <Text style={styles.time}>{messageTime}</Text>
            </>
        );
    };

    const handleMessageThreadUserProfile = () => {
        if (alignment === 'left') {
            if (message?.user?.deleted_at) {
                return (
                    <Pressable style={styles.deletedUserIconWrapper} onPress={() => alert(DELETED_USER_ON_TAP_TEXT)}>
                        <Text style={styles.deletedImageTextStyle}>?</Text>
                    </Pressable>
                );
            } else {
                return message?.user?.image ? (
                    <TouchableOpacity style={styles.imageContainer} onPress={() => onClickUserDp()}>
                        <Image
                            source={{
                                uri: message?.user?.image,
                            }}
                            style={styles.image}
                        />
                    </TouchableOpacity>
                ) : (
                    <Pressable style={styles.imageWrapper1} onPress={() => onClickUserDp()}>
                        {Object.keys(state?.allFriendsId)?.includes(message?.user?.id) ? (
                            <Text style={styles.imageTextStyle}>{message?.user?.first_name.trim('')[0]}</Text>
                        ) : (
                            <Text style={styles.imageTextStyle}>{message?.user?.username.trim('')[0]}</Text>
                        )}
                    </Pressable>
                );
            }
        } else return null;
    };

    const renderGameReview = () => {
        if (!message?.gameReview) return null;

        const { clubName, gameDate, review, photo } = message.gameReview;

        return (
            <TouchableOpacity style={styles.gameReviewContainer} onLongPress={onLongPress}>
                <Text style={styles.gameReviewSubHeader}>{clubName}</Text>
                <Text style={styles.gameReviewDate}>Game Date: {gameDate}</Text>
                {photo && (
                    <TouchableOpacity
                        onPress={() => navigation.navigate('Image Show', { review: message.gameReview })}
                        style={styles.imageWrapper}>
                        <FastImage
                            source={{ uri: photo, priority: FastImage.priority.normal }}
                            style={styles.reviewImage}
                            resizeMode={FastImage.resizeMode.cover}
                        />
                    </TouchableOpacity>
                )}

                <Text style={styles.gameReviewText}>{review}</Text>
            </TouchableOpacity>
        );
    };

    return (
        <>
            <View key={message.id} style={alignment === 'left' ? styles.leftContainer : styles.rightContainer}>
                {handleMessageThreadUserProfile()}

                <View>
                    {alignment === 'left' &&
                        (message?.user?.deleted_at ? (
                            <Text style={styles.deletedUserText}>Deleted User</Text>
                        ) : (
                            <Text style={styles.userName}>
                                {handleMessageScreenParticipantsName(state?.allFriendsId, message?.user, user)}
                            </Text>
                        ))}

                    <View>
                        {message?.type === 'deleted' ? (
                            <View style={styles.chatContainer}>{deletedMessages()}</View>
                        ) : message.text === 'Game Review' && message?.gameReview ? (
                            <View style={styles.chatContainer}>{renderGameReview()}</View>
                        ) : message?.attachments?.length && message?.attachments?.length <= 3 ? (
                            message?.attachments?.map((item, index) => {
                                return (
                                    <View style={styles.chatContainer}>
                                        <ShowChatImages
                                            messageData={message}
                                            alignment={alignment}
                                            onLongPress={onLongPress}
                                            onPressClick={onPressClick}
                                            media={item}
                                            isLast={index === message?.attachments?.length - 1}
                                        />
                                    </View>
                                );
                            })
                        ) : (
                            <View style={styles.chatContainer}>
                                <ShowChatImages
                                    messageData={message}
                                    alignment={alignment}
                                    onLongPress={onLongPress}
                                    onPressClick={onPressClick}
                                />
                            </View>
                        )}
                        <Pressable
                            style={{
                                marginTop: -Spacing.SCALE_10,
                                alignSelf: alignment === 'left' ? 'flex-start' : 'flex-end',
                                right: alignment === 'right' ? Spacing.SCALE_12 : null,
                            }}
                            disabled={reactions?.length < 3}
                            onPress={() => {
                                actions.setReactionState({ message: message, ReactionType: null, reactionListVisible: false, allReactionsListVisible: true, reactions: reactions});
                            }}>
                            {reactions.length > 0 && (
                                <View style={styles.reactionsWrapper}>
                                    {reactions.slice(0, 3).map((item, index) => (
                                        <Pressable
                                            key={index}
                                            style={styles.reaction}
                                            onPress={() => {
                                                actions.setReactionState({ message: message, ReactionType: item?.type, reactionListVisible: true, allReactionsListVisible: false});
                                            }}
                                            disabled={reactions?.length > 3}>
                                            {item.type === 'love' && (
                                                <View style={styles.reactionTextContainer}>
                                                    <Text>{emojis[item.type]}</Text>
                                                </View>
                                            )}
                                            {item.type === 'like' && (
                                                <View style={styles.reactionTextContainer}>
                                                    <Text>{emojis[item.type]}</Text>
                                                </View>
                                            )}
                                            {item.type === 'sad' && (
                                                <View style={styles.reactionTextContainer}>
                                                    <Text>{emojis[item.type]}</Text>
                                                </View>
                                            )}
                                            {item.type === 'haha' && (
                                                <View style={styles.reactionTextContainer}>
                                                    <Text>{emojis[item.type]}</Text>
                                                </View>
                                            )}
                                            {item.type === 'wow' && (
                                                <View style={styles.reactionTextContainer}>
                                                    <Text>{emojis[item.type]}</Text>
                                                </View>
                                            )}
                                        </Pressable>
                                    ))}
                                    {reactions.length > 3 && (
                                        <View style={styles.reactionTextContainer}>
                                            <Text style={[styles.reactionText, { color: colors.greyRgb }]}>
                                                +{reactions.length - 3}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            )}
                        </Pressable>
                        <View style={{ marginTop: Spacing.SCALE_12 }} />
                    </View>
                </View>
            </View>
        </>
    );
};
const makeStyles = (alignment) =>
    StyleSheet.create({
        leftContainer: {
            flexDirection: 'row',
            marginHorizontal: Spacing.SCALE_5,
            marginVertical: Spacing.SCALE_3,
        },

        rightContainer: {},

        imageContainer: {
            alignSelf: 'flex-start',
            marginRight: Spacing.SCALE_6,
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: 75,
            marginTop: Spacing.SCALE_17,
        },
        image: {
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: 75,
        },
        chatContainer: {
            backgroundColor: alignment === 'left' ? '#FFFFFF' : '#098089',
            maxWidth: alignment === 'left' ? Size.SIZE_250 : Size.SIZE_290,
            alignSelf: alignment === 'left' ? 'flex-start' : 'flex-end',
            borderTopLeftRadius: alignment === 'right' ? Spacing.SCALE_8 : null,
            borderTopRightRadius: Spacing.SCALE_8,
            borderBottomLeftRadius: Spacing.SCALE_8,
            borderBottomRightRadius: alignment === 'left' ? Spacing.SCALE_8 : null,
            borderColor: '#ECEBEB',
            borderWidth: alignment === 'left' ? 1 : null,
            marginHorizontal: alignment === 'left' ? null : Spacing.SCALE_16,
            marginVertical: alignment === 'left' ? null : Spacing.SCALE_3,
            minWidth: Spacing.SCALE_120,
        },
        message: {
            color: alignment === 'left' ? '#333333' : '#FFFFFF',
            paddingTop: Spacing.SCALE_10,
            paddingHorizontal: Spacing.SCALE_8,
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Medium',
            lineHeight: Size.SIZE_20,
        },
        time: {
            color: alignment === 'left' ? '#333333' : '#FFFFFF',
            alignSelf: 'flex-end',
            marginRight: alignment === 'left' ? Spacing.SCALE_12 : Spacing.SCALE_6,
            fontSize: Typography.FONT_SIZE_10,
            marginVertical: Spacing.SCALE_8,
            fontFamily: 'Ubuntu-Medium',
        },
        imageWrapper1: {
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: Size.SIZE_100,
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'flex-start',
            backgroundColor: 'rgba(9, 128, 137, 1)',
            marginRight: Spacing.SCALE_6,
            marginTop: Spacing.SCALE_17,
        },
        deletedUserIconWrapper: {
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: Size.SIZE_100,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'white',
            marginRight: Spacing.SCALE_6,
            marginTop: Spacing.SCALE_10,
            alignSelf: 'flex-start',
        },
        imageTextStyle: {
            textAlign: 'center',
            fontSize: Typography.FONT_SIZE_20,
            color: 'rgba(255, 255, 255, 1)',
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            textTransform: 'capitalize',
        },
        deletedImageTextStyle: {
            textAlign: 'center',
            fontSize: 25,
            color: colors.lightShadeGray,
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            textTransform: 'capitalize',
        },
        userName: {
            fontSize: Typography.FONT_SIZE_12,
            color: 'rgba(51,51,51,1)',
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            marginBottom: Spacing.SCALE_1,
        },
        deletedUserText: {
            fontSize: Typography.FONT_SIZE_12,
            color: 'rgba(153,153,153,1)',
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            marginBottom: Spacing.SCALE_1,
        },
        reactionContainer: {
            marginLeft: alignment === 'left' ? 10 : 100,
            alignSelf: alignment === 'left' ? 'flex-start' : 'flex-end',
            paddingRight: alignment === 'left' ? 0 : 20,
            width: '95%',
        },
        reaction: {
            flexDirection: 'row',
        },
        gameReviewContainer: {
            padding: Spacing.SCALE_12,
        },
        gameReviewHeader: {
            fontSize: Typography.FONT_SIZE_16,
            fontFamily: 'Ubuntu-Medium',
            color: alignment === 'left' ? colors.lightBlack : colors.white,
            marginBottom: Spacing.SCALE_8,
            fontWeight: 'bold',
        },
        gameReviewSubHeader: {
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Medium',
            color: alignment === 'left' ? colors.lightBlack : colors.white,
            marginBottom: Spacing.SCALE_4,
            fontWeight: '500',
        },
        gameReviewDate: {
            fontSize: Typography.FONT_SIZE_12,
            fontFamily: 'Ubuntu-Regular',
            color: alignment === 'left' ? colors.darkgray : colors.white,
            marginBottom: Spacing.SCALE_8,
            fontWeight: '400',
        },
        gameReviewText: {
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Regular',
            fontWeight: '400',
            color: alignment === 'left' ? colors.lightBlack : colors.white,
            lineHeight: Size.SIZE_20,
        },
        imageWrapper: {
            marginTop: Spacing.SCALE_8,
            width: '100%',
            marginBottom: Spacing.SCALE_12,
        },
        reviewImage: {
            width: '100%',
            height: 200,
            borderRadius: Spacing.SCALE_8,
        },
        reactionText: {
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Regular',
            color: colors.lightBlack,
            lineHeight: Size.SIZE_18,
            fontWeight: '400',
        },
        reactionTextContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            columnGap: Spacing.SCALE_4,
        },
        reactionsWrapper: {
            flexDirection: 'row',
            flexWrap: 'wrap',
            backgroundColor: colors.whiteRGB,
            paddingHorizontal: Spacing.SCALE_6,
            paddingVertical: Spacing.SCALE_4,
            borderRadius: Size.SIZE_30,
            shadowColor: colors.shadowDarkColor,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            elevation: 5,
        },
    });
export default ShowChatMessage;
