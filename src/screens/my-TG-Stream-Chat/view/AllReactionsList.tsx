import { FlatList, Pressable, StyleSheet, Text, View } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Utils, Constants, Theme and interfaces imports
import { RootStackParamList } from '../../../interface/type';
import { Typography } from '../../../utils/responsiveUI';
import { Size } from '../../../utils/responsiveUI';
import { Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import emojis from '../../../utils/reactions/emoji';
import { Reaction } from '../../../interface';
import { GlobalContext } from '../../../context/contextApi';
interface AllReactionsListProps {
    route: {
        params: {
            reactions: Reaction[];
            fetchedReactions: any;
        };
    };
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

const AllReactionsList = ({ navigation, route }: AllReactionsListProps) => {
    const { state, actions } = useContext(GlobalContext);
    const { reactions, message } = state?.reactionState;
    return (
        <View style={styles.modalContainer}>
            <Pressable
                style={styles.container}
                onPress={() => actions.setReactionState({ allReactionsListVisible: false })}
            />
            <View style={styles.bottomOverlay}>
                <View style={styles.reactionLine} />
                <FlatList
                    data={reactions}
                    renderItem={({ item }) => (
                        <View style={styles.reactionsWrapper}>
                            <Pressable
                                style={styles.reaction}
                                onPress={() =>
                                    actions.setReactionState({
                                        message: message,
                                        ReactionType: item?.type,
                                        reactionListVisible: true,
                                        allReactionsListVisible: false,
                                    })
                                }>
                                {item.type === 'love' && (
                                    <View style={styles.reactionTextContainer}>
                                        <Text>{emojis[item.type as keyof typeof emojis]}</Text>
                                        <Text>{item.count}</Text>
                                    </View>
                                )}
                                {item.type === 'like' && (
                                    <View style={styles.reactionTextContainer}>
                                        <Text>{emojis[item.type as keyof typeof emojis]}</Text>
                                        <Text>{item.count}</Text>
                                    </View>
                                )}
                                {item.type === 'sad' && (
                                    <View style={styles.reactionTextContainer}>
                                        <Text>{emojis[item.type as keyof typeof emojis]}</Text>
                                        <Text>{item.count}</Text>
                                    </View>
                                )}
                                {item.type === 'haha' && (
                                    <View style={styles.reactionTextContainer}>
                                        <Text>{emojis[item.type as keyof typeof emojis]}</Text>
                                        <Text>{item.count}</Text>
                                    </View>
                                )}
                                {item.type === 'wow' && (
                                    <View style={styles.reactionTextContainer}>
                                        <Text>{emojis[item.type as keyof typeof emojis]}</Text>
                                        <Text>{item.count}</Text>
                                    </View>
                                )}
                            </Pressable>
                        </View>
                    )}
                    contentContainerStyle={styles.flatListContent}
                />
            </View>
        </View>
    );
};

export default AllReactionsList;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    modalContainer: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    bottomOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        minHeight: Size.SIZE_160,
        backgroundColor: 'white',
        maxHeight: Size.SIZE_200,
        borderTopLeftRadius: Size.SIZE_12,
        borderTopRightRadius: Size.SIZE_12,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_10,
    },
    reactionLine: {
        height: 4,
        width: 74,
        borderRadius: 20,
        backgroundColor: colors.greyCheckBox,
        alignSelf: 'center',
        marginBottom: Spacing.SCALE_16,
    },
    reactionContainer: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_12,
        alignItems: 'center',
        marginBottom: Spacing.SCALE_10,
    },
    reactionText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: 16,
        color: colors.dark_charcoal,
    },
    reactionsWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_6,
        borderRadius: Size.SIZE_30,
        shadowColor: colors.shadowDarkColor,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    reaction: {
        width: Spacing.SCALE_20,
    },
    reactionTextContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        columnGap: Spacing.SCALE_4,
    },
    flatListContent: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-around',
    },
});
