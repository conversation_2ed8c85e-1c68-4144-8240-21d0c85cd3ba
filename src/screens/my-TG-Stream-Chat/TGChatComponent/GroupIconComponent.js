import React, { useState } from "react";
import { ActivityIndicator, StyleSheet, TouchableOpacity, View, Text, Image, Platform, Alert } from "react-native";
import firebase from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/storage';

import GroupIcon from '../../../assets/svg/GroupIcon.svg'
import { colors } from "../../../theme/theme";
import { Size, Spacing, Typography } from "../../../utils/responsiveUI";
import CameraOptionPopup from "./CameraOptionPopup";
import EditGroupNameIcon from '../../../assets/svg/EditGroupNameIcon.svg';
import useThumbnail from "../../../hooks/useThumbnail";
import constants from "../../../utils/constants/constants";
import {
    pickAndCompressImageFromGallery,
    launchCameraAndCompressImage,
    launchCameraNative,
    compressImage
} from "../../../utils/commonActions";

async function uploadImage({ path }) {
    let firebaseURL = `stream/group-display-pictures-${firebase.auth().currentUser.uid
        }-${Date.now().toString()}`;

    const storageRef = firebase.storage().ref().child(firebaseURL);
    await storageRef.putFile(path);
    return storageRef.getDownloadURL();
}
const platform = Platform.OS

const GroupIconComponent = ({ setLoading, setPhoto, photo, loading, customStyle = {}, screenName }) => {
    const [imageSelectorState, setImageSelectorState] = useState(false)
    const {thumbnailUrl} = useThumbnail(photo, constants.ImageSize[256])

    const uploadPhoto = async (imagePath) => {
        try {
            setLoading(true);
            const photoURL = await uploadImage({ path: imagePath });
            setPhoto(photoURL);
        } catch (error) {
            console.error('Error uploading photo:', error);
            Alert.alert('Error', 'Failed to upload photo. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const showGallery = async () => {
        try {
            const result = await pickAndCompressImageFromGallery(
                {
                    width: 1024,
                    height: 1024,
                    cropping: true,
                    cropperCircleOverlay: true, // Circular crop for group icons
                    cropperToolbarTitle: 'Crop Group Icon',
                    compressImageQuality: 0.8,
                },
                {
                    compressionMethod: 'manual',
                    maxWidth: 1024,
                    quality: 0.7,
                }
            );

            // Use the compressed image for upload
            await uploadPhoto(result.compressedUri);
        } catch (error) {
            console.error('Gallery picker error:', error);
            if (error.message !== 'User cancelled image selection') {
                Alert.alert('Error', 'Failed to select image from gallery');
            }
        }
    };

    const showCamera = async () => {
        try {
            console.log('🚀 GroupIconComponent: Starting camera launch...');

            let result;

            if (Platform.OS === 'ios') {
                // For iOS, use the main camera function which works best
                console.log('� iOS: Using main camera function...');
                try {
                    result = await launchCameraAndCompressImage(
                        {
                            width: 1024,
                            height: 1024,
                            cropping: true,
                            cropperCircleOverlay: true,
                            cropperToolbarTitle: 'Crop Group Icon',
                            compressImageQuality: 0.8,
                        },
                        {
                            compressionMethod: 'manual',
                            maxWidth: 1024,
                            quality: 0.7,
                        }
                    );
                } catch (iosError) {
                    console.log('⚠️ iOS main camera failed, trying native camera...');
                    // Fallback for iOS: try native camera
                    const image = await launchCameraNative();
                    const compressedUri = await compressImage(image.path, {
                        compressionMethod: 'manual',
                        maxWidth: 1024,
                        quality: 0.7,
                    });
                    result = {
                        originalImage: image,
                        compressedUri: compressedUri,
                    };
                }
            } else {
                // For Android, try native camera first (most reliable for file paths)
                console.log('🤖 Android: Using native camera...');
                try {
                    const image = await launchCameraNative();
                    const compressedUri = await compressImage(image.path, {
                        compressionMethod: 'manual',
                        maxWidth: 1024,
                        quality: 0.7,
                    });
                    result = {
                        originalImage: image,
                        compressedUri: compressedUri,
                    };
                } catch (androidError) {
                    console.log('⚠️ Android native camera failed, trying main camera...');
                    // Fallback for Android: try main camera
                    result = await launchCameraAndCompressImage(
                        {
                            width: 1024,
                            height: 1024,
                            cropping: true,
                            cropperCircleOverlay: true,
                            cropperToolbarTitle: 'Crop Group Icon',
                            compressImageQuality: 0.8,
                        },
                        {
                            compressionMethod: 'manual',
                            maxWidth: 1024,
                            quality: 0.7,
                        }
                    );
                }
            }

            console.log('✅ GroupIconComponent: Camera result received:', result);

            // Use the compressed image for upload
            await uploadPhoto(result.compressedUri);
        } catch (error) {
            console.error('❌ GroupIconComponent Camera launcher error:', error);

            if (error.message && error.message.includes('cancelled')) {
                console.log('📱 User cancelled camera');
                return; // Don't show error for user cancellation
            }

            Alert.alert(
                'Camera Error',
                'Unable to open camera. Please check camera permissions and try again.',
                [{ text: 'OK' }]
            );
        }
    };

    return (
        <>
            <TouchableOpacity style={[styles.profileWrapper, customStyle]} onPress={() => setImageSelectorState(!imageSelectorState)}>
                {
                    (photo || loading) ? <><Image
                        source={{ uri: thumbnailUrl }}
                        style={styles.imageStyle}
                    />
                        {loading && (
                            <View style={styles.loaderWrapper}>
                                <ActivityIndicator size="small" color={colors.cyan} />
                            </View>
                        )}
                    </>
                        :
                        <>
                            <GroupIcon />
                            <View style={styles.imageTextWrapper}>
                                <Text style={styles.imageText}>Add Group Icon</Text>
                            </View>
                        </>
                }
            </TouchableOpacity>
            {imageSelectorState && <CameraOptionPopup popupState={[imageSelectorState, setImageSelectorState]} showGallery={showGallery} showCamera={showCamera} />}
            {photo && screenName && <TouchableOpacity
                onPress={() => {
                    setImageSelectorState(!imageSelectorState)
                }}
                style={styles.editBtnWrapper}>
                <EditGroupNameIcon height={22} width={22} />
            </TouchableOpacity>}
        </>
    )
}

export default GroupIconComponent

const styles = StyleSheet.create({
    imageTextWrapper: {
        width: platform === 'android' ? Size.SIZE_60 : Size.SIZE_70,
        alignItems: 'center',
    },
    imageText: {
        fontSize: Typography.FONT_SIZE_10,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        color: 'rgba(49, 49, 49, 1)',
        textAlign: 'center'
    },
    profileWrapper: {
        borderRadius: Size.SIZE_100,
        minWidth: Size.SIZE_70,
        minHeight: Size.SIZE_70,
        backgroundColor: '#DADADA',
        alignSelf: 'center',
        marginTop: Spacing.SCALE_24,
        alignItems: 'center',
        justifyContent: 'center'
    },
    imageStyle: {
        height: Size.SIZE_70,
        width: Size.SIZE_70,
        borderRadius: Size.SIZE_100,
    },
    loaderWrapper: {
        position: 'absolute',
        left: 0,
        right: 0
    },
    editBtnWrapper: {
        backgroundColor: colors.white,
        position: 'absolute',
        zIndex: 100,
        right: Spacing.SCALE_120,
        top: 0,
        width: 30,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 50,
        shadowColor: colors.shadowColors,
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
})