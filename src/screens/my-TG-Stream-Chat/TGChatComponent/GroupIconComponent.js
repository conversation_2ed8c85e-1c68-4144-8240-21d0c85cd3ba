import React, { useState } from "react";
import { ActivityIndicator, StyleSheet, TouchableOpacity, View, Text, Image, Platform, Alert } from "react-native";
import firebase from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/storage';

import GroupIcon from '../../../assets/svg/GroupIcon.svg'
import { colors } from "../../../theme/theme";
import { Size, Spacing, Typography } from "../../../utils/responsiveUI";
import CameraOptionPopup from "./CameraOptionPopup";
import EditGroupNameIcon from '../../../assets/svg/EditGroupNameIcon.svg';
import useThumbnail from "../../../hooks/useThumbnail";
import constants from "../../../utils/constants/constants";
import {
    pickAndCompressImageFromGallery,
    launchCameraAndCompressImage,
    launchCameraExternal,
    launchCameraSimple,
    launchCameraNative,
    compressImage
} from "../../../utils/commonActions";

async function uploadImage({ path }) {
    let firebaseURL = `stream/group-display-pictures-${firebase.auth().currentUser.uid
        }-${Date.now().toString()}`;

    const storageRef = firebase.storage().ref().child(firebaseURL);
    await storageRef.putFile(path);
    return storageRef.getDownloadURL();
}
const platform = Platform.OS

const GroupIconComponent = ({ setLoading, setPhoto, photo, loading, customStyle = {}, screenName }) => {
    const [imageSelectorState, setImageSelectorState] = useState(false)
    const {thumbnailUrl} = useThumbnail(photo, constants.ImageSize[256])

    const uploadPhoto = async (imagePath) => {
        try {
            setLoading(true);
            const photoURL = await uploadImage({ path: imagePath });
            setPhoto(photoURL);
        } catch (error) {
            console.error('Error uploading photo:', error);
            Alert.alert('Error', 'Failed to upload photo. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const showGallery = async () => {
        try {
            const result = await pickAndCompressImageFromGallery(
                {
                    width: 1024,
                    height: 1024,
                    cropping: true,
                    cropperCircleOverlay: true, // Circular crop for group icons
                    cropperToolbarTitle: 'Crop Group Icon',
                    compressImageQuality: 0.8,
                },
                {
                    compressionMethod: 'manual',
                    maxWidth: 1024,
                    quality: 0.7,
                }
            );

            // Use the compressed image for upload
            await uploadPhoto(result.compressedUri);
        } catch (error) {
            console.error('Gallery picker error:', error);
            if (error.message !== 'User cancelled image selection') {
                Alert.alert('Error', 'Failed to select image from gallery');
            }
        }
    };

    const showCamera = async () => {
        try {
            console.log('🚀 GroupIconComponent: Starting camera launch...');

            let result;
            let attempts = 0;
            const maxAttempts = 4;

            while (attempts < maxAttempts && !result) {
                attempts++;
                console.log(`📸 Camera attempt ${attempts}/${maxAttempts}`);

                try {
                    if (attempts === 1) {
                        // First attempt: Try the main camera function
                        console.log('🔄 Trying main camera function...');
                        result = await launchCameraAndCompressImage(
                            {
                                width: 1024,
                                height: 1024,
                                cropping: true,
                                cropperCircleOverlay: true,
                                cropperToolbarTitle: 'Crop Group Icon',
                                compressImageQuality: 0.8,
                            },
                            {
                                compressionMethod: 'manual',
                                maxWidth: 1024,
                                quality: 0.7,
                            }
                        );
                    } else if (attempts === 2) {
                        // Second attempt: Try external storage camera
                        console.log('🔄 Trying external storage camera...');
                        const image = await launchCameraExternal({
                            width: 1024,
                            height: 1024,
                            cropping: true,
                            cropperCircleOverlay: true,
                            cropperToolbarTitle: 'Crop Group Icon',
                            compressImageQuality: 0.8,
                        });

                        const compressedUri = await compressImage(image.path, {
                            compressionMethod: 'manual',
                            maxWidth: 1024,
                            quality: 0.7,
                        });

                        result = {
                            originalImage: image,
                            compressedUri: compressedUri,
                        };
                    } else if (attempts === 3) {
                        // Third attempt: Try simple camera with minimal options
                        console.log('🔄 Trying simple camera...');
                        const image = await launchCameraSimple();

                        const compressedUri = await compressImage(image.path, {
                            compressionMethod: 'manual',
                            maxWidth: 1024,
                            quality: 0.7,
                        });

                        result = {
                            originalImage: image,
                            compressedUri: compressedUri,
                        };
                    } else {
                        // Fourth attempt: Try native camera (no file path issues)
                        console.log('🔄 Trying native camera (final fallback)...');
                        const image = await launchCameraNative();

                        const compressedUri = await compressImage(image.path, {
                            compressionMethod: 'manual',
                            maxWidth: 1024,
                            quality: 0.7,
                        });

                        result = {
                            originalImage: image,
                            compressedUri: compressedUri,
                        };
                    }

                    if (result) {
                        console.log(`✅ Camera attempt ${attempts} succeeded!`);
                        break;
                    }
                } catch (attemptError) {
                    console.error(`❌ Camera attempt ${attempts} failed:`, attemptError.message);

                    // If user cancelled, don't try other methods
                    if (attemptError.message && attemptError.message.includes('cancelled')) {
                        throw attemptError;
                    }

                    // If this is the last attempt, throw the error
                    if (attempts === maxAttempts) {
                        throw attemptError;
                    }

                    // Wait a bit before next attempt
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            if (!result) {
                throw new Error('All camera methods failed');
            }

            console.log('✅ GroupIconComponent: Camera result received:', result);

            // Use the compressed image for upload
            await uploadPhoto(result.compressedUri);
        } catch (error) {
            console.error('❌ GroupIconComponent Camera launcher error:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            if (error.message && error.message.includes('cancelled')) {
                console.log('📱 User cancelled camera');
                return; // Don't show error for user cancellation
            }

            Alert.alert(
                'Camera Error',
                'Unable to open camera. Please check camera permissions and try again.',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Try Again',
                        onPress: () => showCamera()
                    }
                ]
            );
        }
    };

    return (
        <>
            <TouchableOpacity style={[styles.profileWrapper, customStyle]} onPress={() => setImageSelectorState(!imageSelectorState)}>
                {
                    (photo || loading) ? <><Image
                        source={{ uri: thumbnailUrl }}
                        style={styles.imageStyle}
                    />
                        {loading && (
                            <View style={styles.loaderWrapper}>
                                <ActivityIndicator size="small" color={colors.cyan} />
                            </View>
                        )}
                    </>
                        :
                        <>
                            <GroupIcon />
                            <View style={styles.imageTextWrapper}>
                                <Text style={styles.imageText}>Add Group Icon</Text>
                            </View>
                        </>
                }
            </TouchableOpacity>
            {imageSelectorState && <CameraOptionPopup popupState={[imageSelectorState, setImageSelectorState]} showGallery={showGallery} showCamera={showCamera} />}
            {photo && screenName && <TouchableOpacity
                onPress={() => {
                    setImageSelectorState(!imageSelectorState)
                }}
                style={styles.editBtnWrapper}>
                <EditGroupNameIcon height={22} width={22} />
            </TouchableOpacity>}
        </>
    )
}

export default GroupIconComponent

const styles = StyleSheet.create({
    imageTextWrapper: {
        width: platform === 'android' ? Size.SIZE_60 : Size.SIZE_70,
        alignItems: 'center',
    },
    imageText: {
        fontSize: Typography.FONT_SIZE_10,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        color: 'rgba(49, 49, 49, 1)',
        textAlign: 'center'
    },
    profileWrapper: {
        borderRadius: Size.SIZE_100,
        minWidth: Size.SIZE_70,
        minHeight: Size.SIZE_70,
        backgroundColor: '#DADADA',
        alignSelf: 'center',
        marginTop: Spacing.SCALE_24,
        alignItems: 'center',
        justifyContent: 'center'
    },
    imageStyle: {
        height: Size.SIZE_70,
        width: Size.SIZE_70,
        borderRadius: Size.SIZE_100,
    },
    loaderWrapper: {
        position: 'absolute',
        left: 0,
        right: 0
    },
    editBtnWrapper: {
        backgroundColor: colors.white,
        position: 'absolute',
        zIndex: 100,
        right: Spacing.SCALE_120,
        top: 0,
        width: 30,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 50,
        shadowColor: colors.shadowColors,
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
})