import React, { useContext, useEffect, useMemo } from 'react';
import { Alert, Platform, StyleSheet, Text, View } from 'react-native';
import { Channel, MessageInput, MessageList, ThemeProvider } from 'stream-chat-react-native';

import { AuthContext } from '../../../context/AuthContext';
import { GlobalContext } from '../../../context/contextApi';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { handleBlockUser, handleUnBlockUser } from '../action';
import { DELETED_USER_TEXT, ONE_TO_ONE } from '../client';
import { handleOnPress1 } from './AlertComponent';
import getAllChannelMembers from '../groupInfo/action/getAllChannelMembers';
import sortByLastName from '../../my-TG-Stream-Chat/groupInfo/action/sortingGroupMembers';
import { mentionMemberList } from './mentionMemberList';
import <PERSON><PERSON>opup<PERSON> from './MentionPopupUI';
import { colors } from '../../../theme/theme';
import { TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import config from '../../../config';
import { fetcher } from '../../../service/fetcher';
import { GET_PEGBOARD_DETAILS_V3 } from '../../../service/EndPoint';
import showToast from '../../../components/toast/CustomToast';
import CustomAttachButton from './CustomAttachComponent';
import { apiServices } from '../../../service/apiServices';
import { RESOURCE_NO_EXIST } from '../../../utils/constants/strings';
import CustomMessageReactionPicker from './CustomMessageReactionPicker';
import ReactionUsersList from '../view/ReactionUsersList';
import AllReactionsList from '../view/AllReactionsList';
import { supportedReactions } from '../../../utils/constants/constants';

const MessageViewScreen = ({
    StreamButton,
    handleEmptyState,
    handleMessageAction,
    handleDateSeperator,
    MessageSimple,
    channel,
}) => {
    const { user } = useContext(AuthContext);
    const { channelsBlockState } = useContext(StreamChatContext);
    const { state, actions } = useContext(GlobalContext);
    const navigation = useNavigation();
    const { navigate } = navigation;

    const { updatedChannel, type } = useMemo(() => {
        const { updatedChannel } = state;
        const { data: { type } = {} } = channel || {};
        return { updatedChannel, type };
    }, [state, channel]);

    // Get all members belongs to this group
    useEffect(() => {
        getAllMembers();
    }, []);
    // Function to get all members belongs to this group
    const getAllMembers = async () => {
        let members = await getAllChannelMembers(channel, {});
        members = members.sort(sortByLastName);
        actions?.channelMembersAction(members);
    };

    const BlockedInputByOther = () => {
        return (
            <View style={styles.blockedText}>
                <Text style={styles.text}>You can't send message to this user</Text>
            </View>
        );
    };

    const BlockedInput = () => {
        return (
            <View style={styles.blockedText}>
                <Text style={styles.text}>
                    You've blocked this user
                    <Text style={styles.innerText} onPress={() => handleOnPress1(channel, handlePress)}>
                        {' '}
                        {'  Unblock'}
                    </Text>
                </Text>
            </View>
        );
    };

    const DeletedUser = () => {
        return (
            <View style={styles.deletedTextWrapper}>
                <Text style={styles.deletedText}>{DELETED_USER_TEXT}</Text>
            </View>
        );
    };

    const MessageInputHandling = () => {
        if (
            channelsBlockState[channel?.data?.id]?.blocked &&
            !channelsBlockState[channel?.data?.id]?.youHaveBlocked &&
            state?.channelMembers?.length !== 1
        ) {
            return <BlockedInputByOther />;
        } else if (
            channelsBlockState[channel?.data?.id]?.youHaveBlocked &&
            channelsBlockState[channel?.data?.id]?.blocked &&
            state?.channelMembers?.length !== 1
        ) {
            return <BlockedInput />;
        } else {
            if (state?.channelMembers?.length === 1 && type === ONE_TO_ONE) {
                return <DeletedUser />;
            } else return <MessageInput additionalTextInputProps={styles.additionalTextInputStyle} />;
        }
    };
    const handlePress = () => {
        if (channel?.data?.blockedBy) {
            let membersArray = state?.channelMembers;
            let otherUserId = membersArray?.filter((member) => member?.user_id !== user?.id);
            if (channel?.data?.blockedBy !== null && channel?.data?.blockedBy[0] === user?.id) {
                handleUnBlockUser(user?.id, channel?.id, otherUserId[0].user_id, actions);
            } else {
                handleBlockUser(user?.id, channel?.id, otherUserId[0].user_id, actions);
            }
        }
    };

    const theme = {
        messageList: {
            container: {
                backgroundColor: 'rgba(242, 242, 242, 1)',
            },
        },
    };

    const handleGetClubList = async (id) => {
        try {
            fetcher({
                method: 'POST',
                endpoint: GET_PEGBOARD_DETAILS_V3,
                body: {
                    userId: user?.id,
                    pegboardId: id,
                },
            })
                .then((res) => {
                    if (res.status && res?.data?.pegboard?.name) {
                        navigation.navigate(config.routes.PEGBOARD_ADD_CLUB, {
                            pegboardId: id,
                            pegBoardName: res?.data?.pegboard?.name,
                            totalClubs: res?.data?.pegboard?.pegboardClubs?.length,
                            item: { ...res?.data?.pegboard, id },
                        });
                    } else {
                        Alert.alert(
                            '404',
                            'The resource you were trying to find does not exist or has been deleted.',
                            [
                                {
                                    text: 'OK',
                                    onPress: () => {},
                                },
                            ],
                            { cancelable: false },
                        );
                    }
                })
                .catch((err) => {
                    showToast({});
                });
        } catch (error) {
            console.log(error);
        }
    };

    const handleGetOfferDetails = async (offerID) => {
        const payload = {
            userId: user?.id,
            offerId: offerID,
        };

        const offerDetailsRes = await apiServices.getOfferDetails(payload);
        if (offerDetailsRes?.status) {
            navigation.navigate('OfferDetails', { offerID: offerID });
        } else {
            navigation.navigate('DeleteChannelConfirmationPopup', {
                popupSubText: RESOURCE_NO_EXIST,
                firstBtnLabel: 'Cancel',
                secondBtnLabel: 'Ok',
            });
        }
    };

    const handleSystemMessageClick = (message) => {
        const { redirection } = message;
        if (redirection?.eventId) {
            navigate('Event Details', { id: message?.redirection?.eventId });
        } else if (redirection?.pegboardId) {
            handleGetClubList(redirection?.pegboardId);
        } else if (redirection?.offerId) {
            handleGetOfferDetails(redirection?.offerId);
        } else if (redirection?.benefitId) {
            navigate('Benefit Details', { id: message?.redirection?.benefitId });
        }
    };

    // Reusable Row Component
    const InfoRow = ({ label, value }) => (
        <View style={styles.row}>
            <Text style={styles.systemMessageText}>{label}</Text>
            <Text style={styles.systemMessageText}>{value}</Text>
        </View>
    );

    const HandleSystemMessage = ({ message }) => {
        if (message?.text?.length) {
            return (
                <TouchableOpacity style={styles.systemMessage} onPress={() => handleSystemMessageClick(message)}>
                    <View style={styles.systemMessageWrapper}>
                        <Text style={styles.systemMessageText}>
                            {message.text?.replace(
                                '<>',
                                state?.allFriendsId[message?.userData?.id]
                                    ? message?.userData?.name
                                    : message?.userData?.username,
                            )}
                        </Text>
                    </View>
                </TouchableOpacity>
            );
        } else {
            const transformedArray = message?.game_information
                ? Object.entries(message?.game_information).map(([key, value]) => ({
                      label: key,
                      value: value,
                  }))
                : [];
            return (
                <View style={styles.container}>
                    <Text style={styles.title}>Game Information</Text>
                    {transformedArray?.map((item, index) => {
                        return <InfoRow key={index} label={item.label} value={item.value} />;
                    })}
                </View>
            );
        }
    };

    return (
        <>
            <View style={styles.container}>
                <ThemeProvider style={theme}>
                    {channel?.type === ONE_TO_ONE ? (
                        <Channel
                            key={channel?.data?.cid}
                            AttachButton={CustomAttachButton}
                            MessageSimple={MessageSimple}
                            LoadingIndicator={() => null}
                            channel={channel}
                            SendButton={StreamButton}
                            DateHeader={() => null}
                            UnreadMessagesNotification={() => null}
                            EmptyStateIndicator={handleEmptyState}
                            CommandsButton={() => null}
                            messageActions={handleMessageAction}
                            disableIfFrozenChannel={false}
                            initialScrollToFirstUnreadMessage={true}
                            MessageSystem={HandleSystemMessage}
                            LoadingErrorIndicator={() => null}
                            keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : undefined}
                            AutoCompleteSuggestionItem={({ triggerType }) => {
                                if (triggerType === 'mention') {
                                    return null;
                                }
                            }}
                            AutoCompleteSuggestionList={({ triggerType }) => {
                                if (triggerType === 'mention') {
                                    return null;
                                }
                            }}
                            supportedReactions={supportedReactions}
                            MessageReactionPicker={CustomMessageReactionPicker}>
                            <MessageList InlineDateSeparator={handleDateSeperator} isListActive={true} />
                            <MessageInputHandling />
                        </Channel>
                    ) : (
                        <Channel
                            key={channel?.data?.cid}
                            AttachButton={CustomAttachButton}
                            MessageSimple={MessageSimple}
                            LoadingIndicator={() => null}
                            channel={channel}
                            SendButton={StreamButton}
                            UnreadMessagesNotification={() => null}
                            hasCreatePoll={false}
                            DateHeader={() => null}
                            EmptyStateIndicator={handleEmptyState}
                            CommandsButton={() => null}
                            messageActions={handleMessageAction}
                            disableIfFrozenChannel={false}
                            initialScrollToFirstUnreadMessage={true}
                            MessageSystem={HandleSystemMessage}
                            LoadingErrorIndicator={() => null}
                            keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : undefined}
                            AutoCompleteSuggestionItem={({ itemProps, triggerType }) => {
                                mentionMemberList(itemProps, state?.allFriendsId);
                                if (triggerType === 'mention') {
                                    if (!(itemProps?.id === user?.id)) return <MentionPopupUI itemProps={itemProps} />;
                                }
                            }}
                            supportedReactions={supportedReactions}
                            MessageReactionPicker={CustomMessageReactionPicker}>
                            <MessageList InlineDateSeparator={handleDateSeperator} isListActive={true} />
                            <MessageInputHandling />
                        </Channel>
                    )}
                </ThemeProvider>
            </View>
            {state?.reactionState?.reactionListVisible && <ReactionUsersList />}
            {state?.reactionState?.allReactionsListVisible && <AllReactionsList />}
        </>
    );
};

export default MessageViewScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    blockedText: {
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: Spacing.SCALE_14,
    },
    deletedTextWrapper: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: Spacing.SCALE_14,
    },
    text: {
        color: 'rgba(102, 102, 102, 1)',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        lineHeight: Size.SIZE_20,
        fontFamily: 'Ubuntu-Medium',
    },
    deletedText: {
        color: 'rgba(102, 102, 102, 1)',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        lineHeight: Size.SIZE_20,
        fontFamily: 'Ubuntu-Medium',
        textAlign: 'center',
    },
    innerText: {
        color: 'rgba(9, 128, 137, 1)',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        lineHeight: Size.SIZE_20,
        fontFamily: 'Ubuntu-Medium',
    },
    messageList: {
        backgroundColor: 'yellow', // Replace with your desired background color
    },
    additionalTextInputStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: 20,
    },
    systemMessage: {
        paddingHorizontal: Spacing.SCALE_24,
        paddingVertical: Spacing.SCALE_5,
        alignSelf: 'center',
    },
    systemMessageWrapper: {
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_8,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.darkGreySystemMessage,
        alignItems: 'center',
        justifyContent: 'center',
    },
    systemMessageText: {
        textAlign: 'left',
        fontSize: Typography.FONT_SIZE_10,
        color: colors.systemMessageText,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_12,
    },
    title: {
        fontSize: Typography.FONT_SIZE_10,
        fontWeight: '500',
        marginBottom: Spacing.SCALE_10,
        lineHeight: Size.SIZE_12,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Spacing.SCALE_4,
    },
});
