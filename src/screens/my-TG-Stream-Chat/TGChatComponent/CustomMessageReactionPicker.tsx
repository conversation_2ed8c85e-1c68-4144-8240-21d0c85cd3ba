import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import { useMessageContext } from 'stream-chat-react-native';
import { useMessagesContext } from 'stream-chat-react-native';

//utils and theme imports
import { Spacing, Size } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

const CustomMessageReactionPicker = () => {
    const { supportedReactions } = useMessagesContext();
    const { dismissOverlay, handleReaction, message } = useMessageContext();
    const renderItem = ({ item }: { item: any }) => {
        const { type, Icon } = item;
        const isReactedByMe = (message?.own_reactions as any[])?.some((reaction: any) => {
            return reaction.type === type;
        });
        return (
            <TouchableOpacity
                style={[
                    styles.reactionButton,
                    isReactedByMe
                        ? {
                              backgroundColor: colors.blackVariant2,
                          }
                        : {},
                ]}
                onPress={() => {
                    handleReaction?.(type);
                    dismissOverlay();
                }}>
                <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                    <Icon style={{ transform: [{ scale: 0.8 }] }} />
                </View>
            </TouchableOpacity>
        );
    };
    return (
        <View style={styles.wrapper}>
            <FlatList
                columnWrapperStyle={styles.container}
                data={supportedReactions}
                keyExtractor={(item) => item.type}
                numColumns={6}
                renderItem={renderItem}
            />
        </View>
    );
};

export default memo(CustomMessageReactionPicker);

const styles = StyleSheet.create({
    container: {
        marginVertical: 8,
        justifyContent: 'space-between',
    },
    wrapper: {
        paddingVertical: Spacing.SCALE_2,
        paddingHorizontal: Spacing.SCALE_16,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_40,
        width: '90%',
        alignSelf: 'center',
        shadowColor: colors.shadowColor1,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        marginBottom: Spacing.SCALE_10,
    },
    reactionButton: {
        height: Size.SIZE_28,
        width: Size.SIZE_28,
        borderRadius: Size.SIZE_40,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
