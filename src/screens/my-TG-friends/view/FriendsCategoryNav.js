import {useContext, useEffect, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {FlatList} from 'react-native-gesture-handler';
import {useIsFocused} from '@react-navigation/native';

import {colors} from '../../../theme/theme';
import {Typography} from '../../../utils/responsiveUI';
import Send from '../sent/View/Send';
import AllFriends from '../allFriends/view/AllFriends';
import Recieved from '../recieved/view/Recieved';
import Declined from '../decline/view/Declined';
import {GlobalContext} from '../../../context/contextApi';

const screens = ['All Friends', 'Sent', 'Received', 'Declined'];

const FriendsCategoryNav = ({
    tabParams = 0,
    addFriendModal,
    activeSearchIcon,
    setActiveSearchIcon,
    isNavigation,
    navigateFrom,
    openSearchBar,
    setSelectedSubTab,
    setOpenSearchBar = () => {},
}) => {
    const [active, setActive] = useState(screens[tabParams]);
    const {state, actions} = useContext(GlobalContext);
    const {friendTab} = state;
    const isFocused = useIsFocused();
    useEffect(() => {
        setActive(friendTab);
        setSelectedSubTab(friendTab);
    }, [isFocused, friendTab]);

    const onPressHandler = (item) => {
        actions?.setFriendTabNavigation(item);
        setSelectedSubTab(item);
        setActive(item);
    };

    //Function to set load selected screen
    const setActiveScreen = () => {
        switch (active) {
            case screens[0]:
                return (
                    <AllFriends
                        activeSearchIcon={activeSearchIcon}
                        setActiveSearchIcon={setActiveSearchIcon}
                        addFriendModal={addFriendModal}
                        tabParams={0}
                        isNavigation={isNavigation}
                        navigateFrom={navigateFrom}
                        openSearchBar={openSearchBar}
                        setOpenSearchBar={setOpenSearchBar}
                    />
                );
            case screens[1]:
                return <Send addFriendModal={addFriendModal} tabParams={1} />;
            case screens[2]:
                return (
                    <Recieved addFriendModal={addFriendModal} tabParams={2} />
                );
            case screens[3]:
                return (
                    <Declined addFriendModal={addFriendModal} tabParams={3} />
                );
            default:
                break;
        }
    };

    return (
        <View style={styles.container}>
            <View>
                <FlatList
                    style={styles.btnBox}
                    horizontal={true}
                    data={screens}
                    renderItem={({item}) => (
                        <SingleButtonItem
                            item={item}
                            onPress={onPressHandler}
                            selected={active === item}
                        />
                    )}
                    keyExtractor={(item) => item}
                />
            </View>
            <View style={{flex: 1}}>{setActiveScreen()}</View>
        </View>
    );
};

export default FriendsCategoryNav;

//Single tab button component
const SingleButtonItem = ({
    item = '',
    onPress = () => {},
    selected = false,
}) => {
    return (
        <TouchableOpacity
            style={[
                styles.btnContainer,
                selected && styles.activeContainerStyle,
            ]}
            onPress={() => {
                onPress(item);
            }}>
            <Text
                style={[styles.textStyle, selected && styles.activeTextStyle]}>
                {item}
            </Text>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.screenBG
    },
    btnBox: {
        flexDirection: 'row',
        marginRight: 10,
        marginLeft: 10,
        marginTop: 16,
        marginBottom: 16,
    },
    btnContainer: {
        display: 'flex',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 40,
        marginHorizontal: 4,
        borderWidth: 1,
        borderColor: colors.darkCharcoal,
        backgroundColor: colors.screenBG,
    },
    textStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
        color: colors.darkCharcoal,
    },

    activeContainerStyle: {
        backgroundColor: colors.tealRgb,

        borderColor: colors.tealRgb,
    },
    activeTextStyle: {
        color: colors.white,
    },
});
