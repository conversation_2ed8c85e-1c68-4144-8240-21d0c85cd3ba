import { StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import React, { useLayoutEffect, useState, useContext } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import auth from '@react-native-firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Components imports
import AdminHeader from '../../components/layout/AdminHeader';

// interface, theme, utils imports
import { RootStackParamList } from '../../interface/type';
import { colors } from '../../theme/theme';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import TealButtonNew from '../../components/buttons/TealButtonNew';
import { isEmpty } from '../auth/actions';
import { apiServices } from '../../service/apiServices';
import { LogoutIcon } from '../../assets/svg';

const AdminLogin = ({ navigation }: { navigation: NativeStackNavigationProp<RootStackParamList> }) => {
    const [email, setEmail] = useState('');
    const [errors, setErrors] = useState('');
    const [loading, setLoading] = useState(false);

    useLayoutEffect(() => {
        navigation.setOptions({
            header: () => <AdminHeader title="Admin Login" />,
        });
    }, [navigation]);

    const signInAsUser = async () => {
        try {
            setLoading(true);
            const currentUser = auth().currentUser;
            const adminId = currentUser?.uid;

            // Optionally store admin ID
            await AsyncStorage.setItem('tg-admin-id', adminId || '');
            await AsyncStorage.setItem('is-admin', 'true');

            const response = await apiServices.adminLogin(email.trim().toLowerCase());

            if (response.status) {
                await auth().signInWithCustomToken(response.customToken);
                // Automatically navigates if auth state is managed
            } else {
                setErrors('You have sent invalid email');
            }
        } catch (error) {
            console.error('Login error:', error);
        } finally {
            setTimeout(() => {
                setLoading(false);
            }, 1000);
        }
    };

    const handleLogin = () => {
        const checkEmpty: any = isEmpty({ email });
        if (Object.keys(checkEmpty).length > 0) {
            setErrors(checkEmpty?.email);
        } else {
            signInAsUser();
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.emailContainer}>
                <Text style={styles.emailTitle}>Email</Text>
                <TextInput
                    value={email}
                    onChangeText={(text) => {
                        setEmail(text);
                        setErrors('');
                    }}
                    style={styles.emailInput}
                    placeholder="Enter email"
                    keyboardType="email-address"
                    placeholderTextColor={colors.darkgray}
                />
                {errors && <Text style={styles.errorText}>{errors}</Text>}
                <TealButtonNew
                    text={'Login'}
                    onPress={handleLogin}
                    btnStyle={[styles.btnContainerStyle]}
                    textStyle={styles.btnTextStyle}
                    loading={loading}
                />
            </View>
            <TouchableOpacity
                style={styles.logoutContainer}
                onPress={() => {
                    auth().signOut();
                    AsyncStorage.removeItem('tg-admin-id');
                    AsyncStorage.removeItem('is-admin');
                }}>
                <LogoutIcon />
                <Text style={styles.logoutText}>Logout</Text>
            </TouchableOpacity>
        </View>
    );
};

export default AdminLogin;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: Spacing.SCALE_16,
    },
    emailContainer: {
        marginTop: Spacing.SCALE_8,
    },
    emailTitle: {
        color: colors.darkgray,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
    },
    emailInput: {
        borderBottomWidth: 1,
        borderColor: colors.borderColor,
        paddingVertical: Spacing.SCALE_10,
        paddingHorizontal: Spacing.SCALE_2,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
    },
    btnContainerStyle: {
        height: Size.SIZE_40,
        paddingVertical: 0,
        backgroundColor: colors.darkteal,
        borderWidth: 1,
        borderColor: colors.darkteal,
        width: '100%',
        marginTop: Spacing.SCALE_32,
        borderRadius: Spacing.SCALE_8,
    },
    btnTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        lineHeight: Size.SIZE_18,
        color: colors.whiteRGB,
    },
    errorText: {
        color: colors.orange,
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_8,
    },
    logoutContainer: {
        position: 'absolute',
        bottom: Spacing.SCALE_26,
        left: Spacing.SCALE_16,
        flexDirection: 'row',
        alignItems: 'center',
        gap: Spacing.SCALE_8,
    },
    logoutText: {
        color: colors.orange,
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
    },
});
