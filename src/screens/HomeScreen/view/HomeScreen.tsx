import {
    FlatList,
    ImageBackground,
    Platform,
    RefreshControl,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    View,
} from 'react-native';
import React, { memo, useContext, useEffect, useLayoutEffect, useState, useCallback } from 'react';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import { requestNotifications } from 'react-native-permissions';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';

import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import StatsGrid from './StatCard';
import { GlobalContext } from '../../../context/contextApi';
import NotificationCard from './NotificationCard';
import { AuthContext } from '../../../context/AuthContext';
import { getStats } from '../action/fetchStats';
import { handleGetAllFriendsId } from '../../my-TG-Stream-Chat/action';
import { NotificationItem, User } from '../../../interface';
import HomeScreenCommonHeader from '../../../components/homeScreenComponent/HomeScreenCommonHeader';
import HomeScreenPopups from './HomeScreenPopups';
import { apiServices } from '../../../service/apiServices';
import config from '../../../config';

const HomeScreen = () => {
    const focused = useIsFocused();
    const { user } = useContext<{ user: User | null }>(AuthContext);
    const navigation = useNavigation();
    const { state, actions } = useContext(GlobalContext);
    const { stats } = state;
    const [loader, setLoader] = useState(true);
    const [pullToRefresh, setPullToRefresh] = useState(false);

    // Handle initial state when component mounts
    useEffect(() => {
        if (state.isFirstTimeVisitHome) {
            actions.setIsMenuBottomSheetOpen(true);
            actions.setMenuBottomSheetOpenIndex(1);
        }
    }, []);

    // Handle screen focus changes
    useEffect(() => {
        if (focused) {
            // When screen is focused, ensure bottom sheet state is maintained
            actions.setIsMenuBottomSheetOpen(state.isMenuBottomSheetOpen);
            actions.setMenuBottomSheetOpenIndex(state.isMenuBottomSheetOpen ? 1 : 0);
            // Call getNotification whenever the screen comes into focus
            getNotification();
        }
    }, [focused]);

    useLayoutEffect(() => {
        // Get all friends id
        handleGetAllFriendsId(user?.id, actions);
    }, []);

    useEffect(() => {
        if (Platform.OS === 'android') {
            requestNotifications(['alert', 'sound']).then(({ status }) => {});
        }
        fetchData(); // Call the function that waits for both API calls
    }, []);

    const handleTealDotStatus = useCallback(async () => {
        if (user?.id) {
            let res = await apiServices.getTealDotStatus({ userId: user?.id });
            actions.setTealDotStatus(res?.data);
        }
    }, [user?.id]);

    useEffect(() => {
        handleTealDotStatus();
    }, [user?.id, focused, navigation]);

    const fetchData = async () => {
        setLoader(true);
        try {
            await Promise.all([getStatsData(), getNotification(), handleTealDotStatus()]); // Wait until both APIs finish
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setLoader(false); // Hide loader after all APIs are completed
        }
    };

    const getStatsData = async () => {
        let tempStats = JSON.parse(JSON.stringify(stats));
        let statsRes = await getStats();
        if (statsRes?.status) {
            tempStats.map((item: any) => {
                item.value = statsRes?.data[item?.name];
            });
        }
        actions.setStats(tempStats);
    };

    const getNotification = useCallback(async () => {
        if (user?.id) {
            let res = await apiServices.getPlayingCardNotifications(user.id);

            let filteredNotifications = res?.filter((item: any) => !item?.read);
            const seenGameIds = new Map();
            const finalData: NotificationItem[] = [];

            filteredNotifications.forEach((item: NotificationItem) => {
                if (item.type === config.notificationTypeConstants.REQUEST_CHAT_NEW_MESSAGE) {
                    const gameId = item.data?.gameId;
                    if (seenGameIds.has(gameId)) {
                        // Increment count in existing reference
                        seenGameIds.get(gameId).data.count += 1;
                    } else {
                        // Clone and initialize count
                        const newItem = {
                            ...item,
                            data: {
                                ...item.data,
                                count: 1,
                            },
                        };
                        finalData.push(newItem);
                        seenGameIds.set(gameId, newItem);
                    }
                } else {
                    // Push other types directly
                    finalData.push(item);
                }
            });
            actions.setNotifications(finalData);
        }
    }, [user?.id, actions]);

    const handlePullToRefresh = () => {
        actions.setExpanded(false);
        setPullToRefresh(true);
        fetchData();
        setPullToRefresh(false);
    };

    const homeSimmer = () => {
        return (
            <>
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[
                        styles.lastWeekTextStyle,
                        { paddingHorizontal: 0, borderRadius: 10, marginLeft: Spacing.SCALE_5 },
                    ]}
                />
                <FlatList
                    data={[1, 2, 3, 4]}
                    numColumns={2}
                    keyExtractor={(item) => item.toString()}
                    renderItem={({ item }) => (
                        <ShimmerPlaceholder style={styles.card} LinearGradient={LinearGradient} isInteraction>
                            <ShimmerPlaceholder
                                LinearGradient={LinearGradient}
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    columnGap: Spacing.SCALE_8,
                                    marginBottom: Spacing.SCALE_6,
                                    height: Size.SIZE_40,
                                    width: '95%',
                                }}
                            />
                        </ShimmerPlaceholder>
                    )}
                    columnWrapperStyle={styles.row}
                    contentContainerStyle={styles.container}
                    scrollEnabled={false}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[
                        styles.lastWeekTextStyle,
                        { paddingHorizontal: 0, borderRadius: Size.SIZE_10, marginLeft: Spacing.SCALE_5 },
                    ]}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={{
                        height: 92,
                        width: '95%',
                        borderRadius: Size.SIZE_10,
                        marginTop: Spacing.SCALE_12,
                        alignSelf: 'center',
                    }}
                />
            </>
        );
    };

    return (
        <>
            <StatusBar backgroundColor={colors.tealRgb} barStyle="light-content" />
            <View style={styles.container}>
                {/* Header */}
                {/* @ts-ignore */}
                <HomeScreenCommonHeader title="Home" />
                <ScrollView
                    style={styles.body}
                    refreshControl={<RefreshControl refreshing={pullToRefresh} onRefresh={handlePullToRefresh} />}>
                    {loader ? (
                        <View style={{ padding: Spacing.SCALE_10 }}>{homeSimmer()}</View>
                    ) : (
                        <View>
                            <ImageBackground
                                source={require('../../../assets/images/profileBG.png')}
                                style={{
                                    width: '100%',
                                    marginBottom: Spacing.SCALE_28,
                                }}
                                imageStyle={{
                                    borderBottomLeftRadius: Size.SIZE_16,
                                    borderBottomRightRadius: Size.SIZE_16,
                                }}>
                                <Text style={styles.lastWeekTextStyle}>In last 4 weeks</Text>
                                <StatsGrid />
                            </ImageBackground>
                            <NotificationCard getNotification={getNotification} />
                        </View>
                    )}
                </ScrollView>
                <HomeScreenPopups />
            </View>
        </>
    );
};

export default memo(HomeScreen);

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    body: {
        flex: 1,
    },
    lastWeekTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        lineHeight: Size.SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        color: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_16,
    },
    overlay: {
        ...StyleSheet.absoluteFillObject, // Makes it cover the full screen
        backgroundColor: 'rgba(0,0,0,0.5)', // Semi-transparent background
        zIndex: 999, // Ensures it stays on top
    },
    card: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_10,
        margin: Spacing.SCALE_6,
        height: Size.SIZE_60,
    },
    row: {
        justifyContent: 'space-between',
    },
});
