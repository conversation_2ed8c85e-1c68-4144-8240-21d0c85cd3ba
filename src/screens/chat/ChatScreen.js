import React from 'react';
import { View, Text, SafeAreaView, TouchableOpacity, KeyboardAvoidingView, Keyboard, Platform } from 'react-native';
import { colors } from '../../theme/theme';
import { Back } from '../../assets/images/svg';
import RequestChatScreen from '../requests/stream-chat/RequestChatScreen';

export default function ChatScreen({ route, navigation }) {
    const { streamChannelId = null } = route?.params;
    return (
        <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS == 'ios' ? 'padding' : null}>
            <SafeAreaView style={{ flex: 1, backgroundColor: colors.white }} onPress={() => Keyboard.dismiss()}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}>
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        style={{ zIndex: 100, paddingHorizontal: 15 }}>
                        <Back />
                    </TouchableOpacity>
                    <Text
                        style={{
                            fontFamily: 'Ubuntu-Medium',
                            fontSize: 16,
                            marginBottom: 15,
                            marginTop: 15,
                        }}>
                        {`Game ID  `}
                        <Text style={{ color: colors.darkgray }}>#{route?.params?.game_id}</Text>
                    </Text>
                </View>
                <View
                    style={{
                        backgroundColor: 'white',
                        flex: 1,
                        borderTopRightRadius: 25,
                        borderTopLeftRadius: 25,
                        borderBottomLeftRadius: 0,
                        borderBottomRightRadius: 0,
                        overflow: 'hidden',
                    }}>
                    <RequestChatScreen
                        requestId={route?.params?.request_id}
                        requestorUserId={route?.params?.requestor_user_id}
                        hostUserId={route?.params?.host_user_id}
                        gameId={route?.params?.game_id}
                        streamChannelId={streamChannelId}
                        hasMessages={route?.params?.has_messages === null ? false : route?.params?.has_messages}
                        isDeletedUser={route?.params?.isDeletedUser || false}
                        clubName={route?.params?.name}
                    />
                </View>
            </SafeAreaView>
        </KeyboardAvoidingView>
    );
}
