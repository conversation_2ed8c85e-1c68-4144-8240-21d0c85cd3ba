import React, { useContext, useMemo } from 'react';
import { FlatList, Image, Pressable, RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { colors } from '../../../../../theme/theme';
import { Size, Spacing, Typography } from '../../../../../utils/responsiveUI';
import { DeleteGreyIcon, ClubGolfIconTeal, ChatWhiteIcon, HostIcon } from '../../../../../assets/svg';
import { RequestHistoryItem } from '../../../../../interface';
import { handleTimeFormat } from '../../../../../components/timeFormatComponent/handleTimeFormat';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import LinearGradient from 'react-native-linear-gradient';
import { AuthContext } from '../../../../../context/AuthContext';
import RequestEmptyScreen from '../../RequestEmptyScreen';
import HostPreview from '../../HostPreview';
import { RootStackParamList } from '../../../../../interface/type';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { GlobalContext } from '../../../../../context/contextApi';
import EmptyStateScreen from '../../../../my-TG-Stream-Chat/forwardMessage/view/EmptyStateScreen';
import redirectToUserProfile from '../../../action/openUserProfile';
import { REQUEST_CLOSED_MESSAGE } from '../../../../../utils/constants/strings';

interface RequestHistoryListingProps {
    data: RequestHistoryItem[];
    onItemPress: (item: RequestHistoryItem) => void;
    onChatPress: (item: RequestHistoryItem) => void;
    refreshing: boolean;
    onRefresh: () => void;
    limit: number;
    onEndReached: () => void;
    paginationLoader: boolean;
    setPaginationLoader: (value: any) => void;
    handleDelete: (item: RequestHistoryItem) => void;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    isLoading?: boolean;
    searchData: string;
}

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = () => {
        switch (status.toLowerCase()) {
            case 'completed':
            case 'fulfilled':
                return { backgroundColor: colors.lightGreen, color: colors.greenColor };
            case 'cancelled':
                return { backgroundColor: colors.lightOrange, color: colors.orange };
            case 'declined':
                return { backgroundColor: colors.lightOrange, color: colors.orange };
            default:
                return { backgroundColor: colors.darkgray, color: colors.white };
        }
    };

    return (
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor().backgroundColor }]}>
            <Text style={[styles.statusText, { color: getStatusColor().color }]}>{status.toUpperCase()}</Text>
        </View>
    );
};

// User avatar component
const UserAvatar = ({
    name,
    isMyRequest,
    userId,
    navigation,
    user,
    profilePhoto,
    isDeletedUser = false,
}: {
    name: string;
    isMyRequest: boolean;
    userId: string;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    user: any;
    profilePhoto: string | null;
    isDeletedUser: boolean;
}) => {
    return (
        <View style={styles.avatarContainer}>
            <Pressable
                style={[styles.avatar, { backgroundColor: isDeletedUser ? colors.lightShadeGray : colors.white }]}
                onPress={() => redirectToUserProfile({ userId: userId, navigation, user })}>
                {profilePhoto ? (
                    isDeletedUser ? (
                        <Text style={[styles.avatarText, { color: isDeletedUser ? colors.white : colors.tealRgb }]}>
                            {'?'}
                        </Text>
                    ) : (
                        <Image source={{ uri: profilePhoto }} style={styles.avatarImage} />
                    )
                ) : (
                    <Text style={[styles.avatarText, { color: isDeletedUser ? colors.white : colors.tealRgb }]}>
                        {isDeletedUser ? '?' : name?.charAt(0)}
                    </Text>
                )}
            </Pressable>
            <View style={{ rowGap: Spacing.SCALE_4 }}>
                {!isDeletedUser && (
                    <View style={[!isMyRequest ? styles.requesterBadge : styles.hostBadge]}>
                        {isMyRequest ? (
                            <Text style={styles.requesterText}>HOST</Text>
                        ) : (
                            <Text style={styles.hostText}>REQUESTER</Text>
                        )}
                    </View>
                )}
                <Text style={[styles.userName, { color: isDeletedUser ? colors.lightShadeGray : colors.lightBlack }]}>
                    {isDeletedUser ? 'Deleted User' : name}
                </Text>
            </View>
        </View>
    );
};

const RequestHistoryListing = ({
    data,
    onItemPress,
    onChatPress,
    refreshing,
    onRefresh,
    onEndReached,
    paginationLoader,
    handleDelete,
    navigation,
    isLoading,
    searchData,
}: RequestHistoryListingProps) => {
    const { user } = useContext(AuthContext);
    const { state } = useContext(GlobalContext);
    const { unreadChannelsObject } = state;
    const RenderItem = ({ item, index }: { item: RequestHistoryItem; index: number }) => {
        const gameID = item.game_id;
        const hostData = useMemo(() => {
            return state.allRequestHostsData?.find((data: any) => data.request_id === item.request_id)?.hosts || [];
        }, [item, state.allRequestHostsData]);
        const isMyRequest = item.requestor_user_id === user.id;
        const acceptingHosts = hostData?.filter((data: any) => data.id === item?.host_user_id);
        return (
            <TouchableOpacity style={styles.card} onPress={() => onItemPress(item)} key={index}>
                <View style={styles.cardHeader}>
                    <StatusBadge status={item.status} />
                    <TouchableOpacity style={styles.trashIcon} onPress={() => handleDelete(item)}>
                        <DeleteGreyIcon />
                    </TouchableOpacity>
                </View>

                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <View style={styles.clubInfo}>
                        <View style={styles.golfIcon}>
                            <ClubGolfIconTeal />
                        </View>
                        <View style={styles.clubDetails}>
                            <Text style={styles.clubName}>{item.club_name}</Text>
                            <Text style={styles.date}>
                                {handleTimeFormat(
                                    item.status === 'declined' || item.status === 'cancelled'
                                        ? item.end_date
                                        : item.game_date,
                                )}
                            </Text>
                        </View>
                    </View>
                    <Text style={styles.gameId}>#{item.game_id}</Text>
                </View>

                <Text style={styles.message} numberOfLines={3}>
                    {item.message.length > 150 ? item.message.slice(0, 130) + '...' : item.message}
                    {item.message.length > 150 && <Text style={styles.readMoreText}> Read more</Text>}
                </Text>
                {isMyRequest ? (
                    item.status === 'completed' && acceptingHosts?.length > 0 ? (
                        acceptingHosts.map((item: any, index: number) => (
                            <HostPreview
                                hostDataItem={item}
                                index={index}
                                request_id={item.request_id}
                                requestor_user_id={item.requestor_user_id}
                                game_id={gameID}
                                requestor_full_name={item.requestor_full_name}
                                navigation={navigation}
                                lastIndex={acceptingHosts?.length - 1}
                                isAcceptingHost={true}
                                type={'history'}
                            />
                        ))
                    ) : hostData?.length > 0 ? (
                        hostData.map((item: any, index: number) => (
                            <HostPreview
                                hostDataItem={item}
                                index={index}
                                request_id={item.request_id}
                                requestor_user_id={item.requestor_user_id}
                                game_id={gameID}
                                requestor_full_name={item.requestor_full_name}
                                navigation={navigation}
                                lastIndex={hostData?.length - 1}
                                type={'history'}
                            />
                        ))
                    ) : (
                        <View style={styles.noHostsContainer}>
                            <View style={styles.hostIconWrapper}>
                                <HostIcon />
                            </View>
                            <Text style={styles.noHostsText}>No Hosts</Text>
                        </View>
                    )
                ) : (
                    <View style={styles.userInfo}>
                        <UserAvatar
                            name={
                                state?.allFriendsId[item?.requestor_user_id]
                                    ? item?.requestor_full_name
                                    : item?.requestor_username
                            }
                            isMyRequest={isMyRequest}
                            userId={item?.requestor_user_id}
                            navigation={navigation}
                            user={user}
                            profilePhoto={item?.requestor_profile_photo}
                            isDeletedUser={item?.requestor_username.includes('deleted')}
                        />
                        <TouchableOpacity
                            style={[styles.chatButton]}
                            onPress={() => {
                                navigation.navigate('DeleteChannelConfirmationPopup', {
                                    handleYesButton: async () => {
                                        onChatPress(item);
                                    },
                                    popupHeader: 'Request Closed',
                                    popupSubText: REQUEST_CLOSED_MESSAGE,
                                    firstBtnLabel: 'No',
                                    secondBtnLabel: 'Yes',
                                });
                            }}>
                            <ChatWhiteIcon />
                            {Array.isArray(unreadChannelsObject) &&
                            unreadChannelsObject?.filter((data: any) => {
                                return data?.id === item?.stream_channel_id;
                            })?.length ? (
                                <View style={styles.tealDotStyle} />
                            ) : null}
                        </TouchableOpacity>
                    </View>
                )}
            </TouchableOpacity>
        );
    };

    return (
        <FlatList
            data={data}
            renderItem={({ item, index }) => <RenderItem item={item} index={index} />}
            keyExtractor={(item) => item.request_id}
            contentContainerStyle={[styles.listContainer]}
            showsVerticalScrollIndicator={false}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            onEndReached={data.length > 0 ? onEndReached : null}
            onEndReachedThreshold={0.5}
            ListEmptyComponent={
                isLoading ? null : searchData ? (
                    <View style={{ marginTop: '40%' }}>
                        <EmptyStateScreen />
                    </View>
                ) : (
                    <RequestEmptyScreen wrapperStyle={{ marginTop: Size.SIZE_150 }} />
                )
            }
            ListFooterComponent={() => {
                if (paginationLoader) {
                    return (
                        <View style={styles.historySimmerContainer}>
                            <View style={styles.historySimmerRow}>
                                <ShimmerPlaceholder
                                    LinearGradient={LinearGradient}
                                    style={styles.historySimmerRowText}
                                />
                                <ShimmerPlaceholder
                                    LinearGradient={LinearGradient}
                                    style={[styles.historySimmerRowText, { width: 24, height: Spacing.SCALE_30 }]}
                                />
                            </View>
                            <View style={styles.historySimmerRow}>
                                <ShimmerPlaceholder
                                    LinearGradient={LinearGradient}
                                    style={[styles.lastWeekTextStyle, { width: '50%' }]}
                                />
                                <ShimmerPlaceholder
                                    LinearGradient={LinearGradient}
                                    style={[styles.lastWeekTextStyle, { width: '10%', height: 15 }]}
                                />
                            </View>
                            <ShimmerPlaceholder
                                LinearGradient={LinearGradient}
                                style={[styles.lastWeekTextStyle, { height: 16 }]}
                            />
                            <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.lastWeekTextStyle} />
                        </View>
                    );
                } else {
                    return null;
                }
            }}
        />
    );
};

const styles = StyleSheet.create({
    listContainer: {},
    card: {
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_10,
        paddingVertical: Spacing.SCALE_16,
        paddingHorizontal: Spacing.SCALE_12,
        marginBottom: Spacing.SCALE_12,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_12,
    },
    statusBadge: {
        padding: Spacing.SCALE_4,
        borderRadius: Size.SIZE_6,
    },
    statusText: {
        fontSize: Typography.FONT_SIZE_9,
        fontWeight: '500',
        lineHeight: Size.SIZE_10,
        fontFamily: 'Ubuntu-Medium',
    },
    trashIcon: {
        padding: Spacing.SCALE_4,
    },
    clubInfo: {
        flexDirection: 'row',
        marginBottom: Spacing.SCALE_14,
    },
    golfIcon: {
        marginRight: Spacing.SCALE_10,
    },
    clubDetails: {},
    clubName: {
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        width: Size.SIZE_220,
    },
    gameId: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkgray,
        marginTop: Spacing.SCALE_2,
    },
    date: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.fadeBlack,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_4,
    },
    message: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
        marginBottom: Spacing.SCALE_14,
        lineHeight: Typography.FONT_SIZE_16,
    },
    readMoreText: {
        color: colors.tealRgb,
        fontSize: Size.SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        marginTop: Spacing.SCALE_4,
    },
    readMore: {
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
    },
    userInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: colors.lightgray,
        borderRadius: Size.SIZE_12,
        padding: Spacing.SCALE_10,
    },
    avatarContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    avatar: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_16,
        backgroundColor: colors.white,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: Spacing.SCALE_8,
    },
    avatarText: {
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        color: colors.tealRgb,
    },
    avatarImage: {
        width: Size.SIZE_32,
        height: Size.SIZE_32,
        borderRadius: Size.SIZE_16,
    },
    requesterBadge: {
        backgroundColor: colors.opacityTeal,
        paddingHorizontal: Spacing.SCALE_4,
        paddingVertical: Spacing.SCALE_2,
        borderRadius: Size.SIZE_16,
        width: Size.SIZE_64,
        alignItems: 'center',
    },
    requesterText: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_9,
        lineHeight: Size.SIZE_10,
        fontFamily: 'Ubuntu-Medium',
    },
    hostBadge: {
        backgroundColor: colors.opacityTeal,
        paddingHorizontal: Spacing.SCALE_4,
        paddingVertical: Spacing.SCALE_2,
        borderRadius: Size.SIZE_16,
        width: Size.SIZE_32,
    },
    hostText: {
        color: colors.tealRgb,
        fontSize: Typography.FONT_SIZE_9,
        lineHeight: Size.SIZE_10,
        fontFamily: 'Ubuntu-Medium',
    },
    userName: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
    chatButton: {
        width: Size.SIZE_30,
        height: Size.SIZE_30,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
    },
    historySimmerContainer: {
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_16,
        borderRadius: Spacing.SCALE_16,
        marginBottom: Spacing.SCALE_8,
    },
    historySimmerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    historySimmerRowText: {
        width: 62,
        height: 18,
        paddingHorizontal: 0,
        borderRadius: 10,
    },
    lastWeekTextStyle: {
        height: 50,
        width: '100%',
        paddingHorizontal: 0,
        borderRadius: 10,
        marginTop: Spacing.SCALE_14,
    },
    hostIconWrapper: {
        backgroundColor: colors.requestedBtnColor,
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    noHostsContainer: {
        marginTop: Spacing.SCALE_12,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    noHostsText: {
        fontSize: Size.SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    tealDotStyle: {
        width: 10,
        height: 10,
        borderRadius: 50,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        right: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        top: -2,
    },
});

export default RequestHistoryListing;
