import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import HostPreview from './HostPreview';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { HostIcon } from '../../../assets/svg';
import { colors } from '../../../theme/theme';
import { ACCEPTED } from '../../../utils/constants/strings';

interface RenderHostsProps {
    hostData: any[];
    type: string;
    navigation: any;
    unreadChannels: any[];
    request_id: string;
    requestor_user_id: string;
    game_id: number;
    requestor_full_name: string;
    userInfo: any;
    request?: any;
}

const RenderHosts = ({
    hostData,
    type,
    navigation,
    request_id,
    requestor_user_id,
    game_id,
    requestor_full_name,
    userInfo,
    request,
}: RenderHostsProps) => {
    const otherHosts = hostData?.filter((item: any) => item.id !== userInfo?.userId);
    const acceptingHosts = hostData?.filter((item: any) => item.id === userInfo?.userId);
    if (
        (type.toLowerCase().includes('requested-history') &&
            (request?.status != 'cancelled' && request?.status != 'declined')) ||
        type === 'requested-accepted'
    ) {
        return (
            <>
                {acceptingHosts?.length > 0 && <Text style={styles.title}>{'Accepting Host'}</Text>}
                {acceptingHosts?.length > 0 && (
                    <View style={[styles.box, { paddingVertical: Spacing.SCALE_12 }]}>
                        {acceptingHosts
                            .filter((item: any) => item.id === userInfo?.userId)
                            .map((item: any, index: number) => (
                                <HostPreview
                                    key={item.id}
                                    hostDataItem={item}
                                    index={index}
                                    request_id={request_id}
                                    requestor_user_id={requestor_user_id}
                                    game_id={game_id}
                                    requestor_full_name={requestor_full_name}
                                    navigation={navigation}
                                    lastIndex={0}
                                    isAcceptingHost={true}
                                    type={type}
                                />
                            ))}
                    </View>
                )}
                {otherHosts?.length > 0 && <Text style={styles.title}>{'Other Hosts'}</Text>}
                {otherHosts?.length > 0 && (
                    <View style={[styles.box, { paddingVertical: Spacing.SCALE_12 }]}>
                        {otherHosts?.map((item: any, index: number) => (
                            <HostPreview
                                key={item.id}
                                hostDataItem={item}
                                index={index}
                                request_id={request_id}
                                requestor_user_id={requestor_user_id}
                                game_id={game_id}
                                requestor_full_name={requestor_full_name}
                                navigation={navigation}
                                lastIndex={otherHosts.length - 1}
                                type={type}
                            />
                        ))}
                    </View>
                )}
            </>
        );
    } else {
        return (
            <>
                <Text style={styles.title}>{'Hosts'}</Text>
                <View style={[styles.box]}>
                    {hostData?.length > 0 ? (
                        hostData.map((item: any, index: number) => (
                            <HostPreview
                                key={item.id}
                                hostDataItem={item}
                                index={index}
                                request_id={request_id}
                                requestor_user_id={requestor_user_id}
                                game_id={game_id}
                                requestor_full_name={requestor_full_name}
                                navigation={navigation}
                                lastIndex={hostData.length - 1}
                            />
                        ))
                    ) : (
                        <View style={styles.noHostsContainer}>
                            <View style={styles.hostIconWrapper}>
                                <HostIcon />
                            </View>
                            <Text style={styles.noHostsText}>No hosts yet</Text>
                        </View>
                    )}
                </View>
            </>
        );
    }
};

export default RenderHosts;

const styles = StyleSheet.create({
    title: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    box: {
        backgroundColor: colors.whiteRGB,
        // paddingVertical: Spacing.SCALE_16,
        paddingHorizontal: Spacing.SCALE_12,
        borderRadius: Spacing.SCALE_12,
        marginTop: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_16,
    },
    noHostsText: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    hostIconWrapper: {
        backgroundColor: colors.requestedBtnColor,
        height: Size.SIZE_32,
        width: Size.SIZE_32,
        borderRadius: Size.SIZE_50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    noHostsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
        paddingVertical: Spacing.SCALE_12,
    },
});
