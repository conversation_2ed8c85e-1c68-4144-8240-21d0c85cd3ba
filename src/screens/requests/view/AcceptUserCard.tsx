import { Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useMemo } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { Spacing, Size } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { HostData, HostDataInterface, RequestedAcceptedRequest } from '../../../interface';
import { AuthContext } from '../../../context/AuthContext';
import { ChatWhiteIcon } from '../../../assets/svg';
import showToast from '../../../components/toast/CustomToast';
import { RootStackParamList } from '../../../interface/type';
import { GlobalContext } from '../../../context/contextApi';
import redirectToUserProfile from '../action/openUserProfile';

interface AcceptUserCardProps {
    isRequester: boolean;
    item: RequestedAcceptedRequest;
    hostData: HostData[];
    unreadChannelsObject?: any;
    hostDataItem?: HostDataInterface;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    type?: string;
}

const AcceptUserCard = ({
    isRequester,
    item,
    hostData,
    unreadChannelsObject,
    hostDataItem,
    navigation,
    type,
}: AcceptUserCardProps) => {
    const acceptedHostData: HostDataInterface = hostData.filter((data: any) => data.id === item.game_host_user_id)[0];
    const { user } = useContext(AuthContext);
    const { state } = useContext(GlobalContext);

    const userData = useMemo(() => {
        const userData = isRequester
            ? {
                  profilePhoto: acceptedHostData?.profilePhoto || null,
                  name: item.game_host_full_name || '',
                  stream_channel_id: acceptedHostData?.stream_channel_id || '',
                  requestor_user_id: acceptedHostData?.id || '',
                  requestor_full_name: item.game_host_full_name || '',
                  has_messages: acceptedHostData?.has_messages || false,
                  host_user_id: acceptedHostData?.id || '',
                  userName: item.game_host_user_name,
                  userID: item?.game_host_user_id,
              }
            : {
                  profilePhoto: item?.requestor_profile_photo || null,
                  name: item.requestor_full_name || '',
                  stream_channel_id: item.stream_channel_id || '',
                  requestor_user_id: item.requestor_user_id || '',
                  requestor_full_name: item.requestor_full_name || '',
                  has_messages: item.has_messages || false,
                  host_user_id: item.host_user_id || '',
                  userName: item.requestor_username,
                  userID: item?.requestor_user_id,
              };
        return userData;
    }, [isRequester, hostData, user]);

    const goToChatScreen = () => {
        if (userData?.host_user_id && userData?.requestor_user_id && item?.host_user_id !== item?.requestor_user_id) {
            navigation.navigate('ChatDetails', {
                type: 'received-open',
                request_id: item.request_id,
                game_id: item.game_id,
                streamChannelId: userData?.stream_channel_id,
                requestor_user_id: userData?.requestor_user_id,
                requestor_full_name: userData?.requestor_full_name,
                host_user_id: userData?.host_user_id,
                has_messages: userData?.has_messages,
                name: item?.club_name,
            });
        } else {
            showToast({});
        }
    };

    return (
        <View
            style={{
                backgroundColor: colors.lightgray,
                marginTop: Spacing.SCALE_12,
                borderRadius: Size.SIZE_12,
                paddingHorizontal: Spacing.SCALE_12,
                paddingVertical: Spacing.SCALE_10,
            }}>
            <View style={styles.userCardWrapper}>
                <View style={styles.userCardHeader}>
                    <Pressable
                        style={styles.userCardProfile}
                        onPress={() => {
                            if (userData.userName.includes('deleted')) {
                                return;
                            } else {
                                redirectToUserProfile({ userId: userData.userID, navigation, user });
                            }
                        }}>
                        {userData.profilePhoto ? (
                            <Image source={{ uri: userData.profilePhoto }} style={styles.userCardProfile} />
                        ) : (
                            <View style={styles.userCardProfile}>
                                <Text style={styles.userCardProfileText}>{userData?.name?.charAt(0)}</Text>
                            </View>
                        )}
                    </Pressable>
                    <View style={{ rowGap: Spacing.SCALE_4 }}>
                        <View
                            style={[styles.userCardRoleWrapper, { width: isRequester ? Size.SIZE_36 : Size.SIZE_64 }]}>
                            <Text style={styles.userCardRoleText}>{isRequester ? 'Host' : 'Requester'}</Text>
                        </View>
                        <Text style={styles.userCardNameText}>
                            {isRequester
                                ? state?.allFriendsId[userData?.userID]
                                    ? userData?.name.includes('deleted')
                                        ? 'deleted user'
                                        : userData?.name
                                    : userData?.userName.includes('deleted')
                                    ? 'deleted user'
                                    : userData?.name
                                : userData?.name.includes('deleted')
                                ? 'deleted user'
                                : userData?.name}{' '}
                            {!isRequester && item?.number_of_players > 1 && (
                                <Text>
                                    {`  `}+{item?.number_of_players - 1}
                                </Text>
                            )}
                        </Text>
                    </View>
                </View>
                <TouchableOpacity style={styles.chatIconWrapper} onPress={goToChatScreen}>
                    <ChatWhiteIcon width={Size.SIZE_14} height={Size.SIZE_13} />
                    {Array.isArray(unreadChannelsObject) &&
                    unreadChannelsObject?.filter((data: any) => {
                        return data.id === acceptedHostData?.stream_channel_id;
                    })?.length ? (
                        <View style={styles.tealDotStyle} />
                    ) : null}
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default AcceptUserCard;

const styles = StyleSheet.create({
    userCardWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    userCardProfile: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
    },
    userCardRoleText: {
        fontSize: Size.SIZE_9,
        fontWeight: '500',
        color: colors.tealRgb,
        lineHeight: Size.SIZE_10,
        fontFamily: 'Ubuntu-Medium',
        textTransform: 'uppercase',
    },
    userCardProfileText: {
        fontSize: Size.SIZE_12,
        fontWeight: '500',
        color: colors.white,
        lineHeight: Size.SIZE_12,
        fontFamily: 'Ubuntu-Medium',
    },
    userCardNameText: {
        fontSize: Size.SIZE_14,
        fontWeight: '400',
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Regular',
    },
    userCardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
    },
    userCardRoleWrapper: {
        paddingHorizontal: Spacing.SCALE_4,
        paddingVertical: Spacing.SCALE_2,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.opacityTeal,
        borderRadius: Size.SIZE_16,
    },
    chatIconWrapper: {
        width: Size.SIZE_30,
        height: Size.SIZE_30,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: Size.SIZE_8,
    },
    tealDotStyle: {
        width: 10,
        height: 10,
        borderRadius: 50,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        right: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        top: -2,
    },
});
