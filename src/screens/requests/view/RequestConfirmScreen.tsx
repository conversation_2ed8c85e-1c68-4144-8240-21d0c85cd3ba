import {
    StyleSheet,
    Text,
    TextInput,
    View,
    KeyboardAvoidingView,
    Platform,
    Pressable,
    Keyboard,
    TouchableOpacity,
} from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';

//Custom imports
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import { ClubGolfIconTeal, DatePickerSvgIcon } from '../../../assets/svg';
import { RootStackParamList } from '../../../interface/type';
import { ACCEPT_REQUEST_AS_HOST, DECLINE_REQUEST_AS_HOST, DELETE_GAME_REQUEST } from '../../../service/EndPoint';
import { AuthContext } from '../../../context/AuthContext';
import { fetcher } from '../../../service/fetcher';
import showToast from '../../../components/toast/CustomToast';
import { getAllRequest } from '../action/getAllRequest';
import { GlobalContext } from '../../../context/contextApi';
import { DeleteRequestParams } from '../../../interface';
import { handleDeleteRequestedRequest } from '../action/handleDeleteRequest';

//Utils imports
import { Size } from '../../../utils/responsiveUI';
import { Spacing } from '../../../utils/responsiveUI';
import { Typography } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import {
    DECLINING_REQUEST_LAST_MINUTE_SHORT_NOTICE,
    DELETE_REQUEST_CONFIRMATION_TEXT,
    ERROR,
    PLEASE_SELECT_THE_DATE_AVAILABLE_TEXT,
    REMOVE_REQUEST,
    REQUEST_CANCELLED_AND_MOVED_TO_HISTORY,
    SUCCESS,
} from '../../../utils/constants/strings';
import useQuery from '../../../hooks/useQuery';
import AcceptRequestModalNew from '../../../components/modals/AcceptRequestModalNew';
import { handleTimeFormat } from '../../../components/timeFormatComponent/handleTimeFormat';
import { createGameRequest } from '../../../service/apiServices/createGameRequest';

interface DeclineRequestParams {
    requestId: any;
    userId: any;
    deleteReason?: string;
}

// Requested request
// 1. Requested Open
// Api: DELETE_REQUESTED_REQUEST

// 2.Requested Accepted
// Api: DELETE_REQUESTED_REQUEST

// Received request
// 1. Received open
// Api: DECLINE_REQUEST_AS_HOST

// 2. Received accepted
// Api: DECLINE_REQUEST_AS_HOST

const RequestConfirmScreen = ({ route }: { route: any }) => {
    const {
        popupType = '',
        game_id,
        requestor_full_name,
        acceptRequest,
        chatAgainCallBack,
        handleYesButton,
        declineRequest,
        request,
        deleteRequest,
        request_id,
        type = '',
        callBack = () => {},
        showReason = false,
        hostData = [],
    } = route.params;
    const { user } = useContext(AuthContext);
    const { actions } = useContext(GlobalContext);
    const [showNeedToLogisticPopup, setShowNeedToLogisticPopup] = useState(false);
    const [showDateConfirmation, setShowDateConfirmation] = useState(false);
    const [showCalendar, setShowCalendar] = useState(false);
    const [reason, setReason] = useState('');
    const [reasonError, setReasonError] = useState('');
    const [isWithin48Hours, setIsWithin48Hours] = useState(false);
    const [date, setDate] = useState('');
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

    const { data } = useQuery(`
        {
            system_setting(where: {name: {_eq: "last_minute_request_cancellation_threshold"}}) {
                value
            }
        }
        `);
    const thresholdTime = data?.system_setting[0]?.value;

    const isReasonRequired = () => {
        if (type.includes('accepted')) {
            true;
            // return request?.requestor_user_id === user?.id ? true : false;
        } else if (type === 'requested-open') {
            return hostData.length > 0;
        } else if (type === 'received-open') {
            return !!(request?.has_messages || request?.requester_has_message);
        }
        if (type?.includes('requested') || type?.includes('received')) {
            return true;
        }
        return false;
    };

    useEffect(() => {
        // Check if game date is within 48 hours
        if (request?.game_date) {
            const gameDate = moment(request?.game_date);
            const now = moment();
            const hoursDifference = gameDate.diff(now, 'hours');
            console.log('objectthresholdTime', thresholdTime);
            setIsWithin48Hours(hoursDifference <= thresholdTime?.value);
        }
    }, [request?.game_date, thresholdTime]);

    const validateReason = () => {
        if (isReasonRequired() && !reason.trim()) {
            setReasonError('Please provide a reason');
            return false;
        }
        setReasonError('');
        return true;
    };

    async function onDeclineRequest() {
        if (!validateReason()) return;

        const declineRequestParams: DeclineRequestParams = {
            requestId: request.request_id,
            userId: user?.id,
        };
        if (reason) {
            declineRequestParams.deleteReason = reason;
        }
        fetcher({
            endpoint: DECLINE_REQUEST_AS_HOST,
            method: 'POST',
            body: declineRequestParams,
        })
            .then((res) => {
                if (res?.status) {
                    setReason('');
                    getAllRequest(actions, user?.id);
                    navigation.goBack();
                    setTimeout(() => {
                        callBack();
                    }, 100);
                    showToast({ type: SUCCESS, header: '', message: REQUEST_CANCELLED_AND_MOVED_TO_HISTORY });
                } else {
                    showToast({});
                }
            })
            .catch(() => {
                showToast({});
            });
    }

    const handleDeleteHistoryRequest = () => {
        const deleteRequestParams = {
            userId: user?.id,
            requestId: request?.request_id,
        };
        fetcher({
            endpoint: DELETE_GAME_REQUEST,
            method: 'POST',
            body: deleteRequestParams,
        })
            .then((res) => {
                if (res?.status) {
                    navigation.goBack();
                    setTimeout(() => {
                        callBack();
                    }, 100);
                } else {
                    showToast({});
                }
            })
            .catch(() => {
                showToast({});
            });
    };

    const handleDeleteRequest = (reason: string) => {
        if (!validateReason()) return;

        const deleteRequestParams: DeleteRequestParams = {
            userId: user?.id,
            requestId: request_id,
        };
        if (reason) {
            deleteRequestParams.deleteReason = reason;
        }
        handleDeleteRequestedRequest(deleteRequestParams, navigation, callBack, type);
    };

    // Function to accept request as a host
    async function createGame() {
        actions.setAppLoader(true);
        setShowCalendar(false);
        if (date) {
            const acceptRequestParams = {
                userId: user?.id,
                gameDate: moment(date).format('YYYY-MM-DD'),
                requestId: request?.request_id,
            };

            let res = await createGameRequest(acceptRequestParams);
            if (res?.status) {
                if (res?.status) {
                    callBack();
                    actions.setAppLoader(false);
                    navigation.goBack();
                } else {
                    showToast({});
                    actions.setAppLoader(false);
                }
            }
        } else {
            showToast({ type: ERROR, message: 'Date is required' });
        }
    }

    if (type === 'history') {
        return (
            <View style={{ flex: 1, backgroundColor: colors.transparentRgba }}>
                <View style={styles.container}>
                    <View style={styles.centerElements}>
                        <Text style={[styles.headerText, { marginBottom: Spacing.SCALE_16 }]}>Remove Request</Text>
                        <Text style={styles.body}>{DELETE_REQUEST_CONFIRMATION_TEXT}</Text>
                    </View>
                    <View style={styles.btnWrapper}>
                        <TealButtonNew
                            text={'Dismiss'}
                            btnStyle={[styles.btnStyle, { backgroundColor: colors.lightgray }]}
                            textStyle={styles.dismissTextStyle}
                            onPress={() => {
                                navigation.goBack();
                            }}
                            disabled={false}
                            loading={false}
                        />
                        <TealButtonNew
                            text={REMOVE_REQUEST}
                            btnStyle={[
                                styles.btnStyle,
                                {
                                    backgroundColor: colors.orange,
                                },
                            ]}
                            textStyle={styles.unmuteTextStyle}
                            onPress={() => {
                                handleDeleteHistoryRequest();
                            }}
                            disabled={false}
                            loading={false}
                        />
                    </View>
                </View>
            </View>
        );
    } else
        return (
            <>
                <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={{ flex: 1 }}>
                    <Pressable
                        style={{ flex: 1, backgroundColor: colors.transparentRgba }}
                        onPress={() => Keyboard.dismiss()}>
                        <View style={styles.container}>
                            <View style={styles.centerElements}>
                                {!declineRequest && (
                                    <View style={styles.headerIconWrapper}>
                                        <ClubGolfIconTeal width={Size.SIZE_30} height={Size.SIZE_32} />
                                    </View>
                                )}
                                <Text style={[styles.headerText, { marginBottom: Spacing.SCALE_8 }]}>
                                    {(type === 'received-accepted' ||
                                        type === 'requested-accepted' ||
                                        type === 'requested-open') &&
                                    showReason
                                        ? 'Cancel Request'
                                        : type === 'received-open' && showReason
                                        ? 'Decline Request'
                                        : popupType === 'cancel'
                                        ? 'Cancel Request'
                                        : declineRequest
                                        ? 'Decline Request'
                                        : 'Accept Request Confirmation'}
                                </Text>

                                {/* Sub header text UI */}
                                {declineRequest ? (
                                    <>
                                        <Text
                                            style={[
                                                styles.body,
                                                (type === 'received-accepted' || type === 'requested-accepted') && {
                                                    marginBottom: Spacing.SCALE_12,
                                                },
                                            ]}>
                                            {`Are you sure that you would like to ${
                                                type === 'received-open' ? 'decline' : 'cancel'
                                            } this request?`}
                                        </Text>
                                    </>
                                ) : deleteRequest ? (
                                    <Text style={styles.body}>Are you sure you would like to cancel this request?</Text>
                                ) : showNeedToLogisticPopup || !acceptRequest ? (
                                    <Text style={styles.body}>
                                        You must finalize the logistics before accepting the request. Chat with{' '}
                                        <Text style={{ color: colors.tealRgb }}>{requestor_full_name}</Text> again or
                                        cancel anyways.
                                    </Text>
                                ) : showDateConfirmation ? (
                                    <Text style={styles.body}>{PLEASE_SELECT_THE_DATE_AVAILABLE_TEXT}</Text>
                                ) : (
                                    <Text style={styles.body}>
                                        Have you confirmed the logistics for{' '}
                                        <Text style={{ color: colors.tealRgb }}>#{game_id}</Text> with{' '}
                                        <Text style={{ color: colors.tealRgb }}>{requestor_full_name}</Text>?
                                    </Text>
                                )}

                                {/* Notice text UI */}
                                {(type === 'received-accepted' || type === 'requested-accepted') && isWithin48Hours && (
                                    <Text style={styles.declineBody}>{DECLINING_REQUEST_LAST_MINUTE_SHORT_NOTICE}</Text>
                                )}

                                {/* Reason text UI */}
                                {showReason && (
                                    <>
                                        <Text style={styles.reasonText}>
                                            Add Reason {isReasonRequired() && <Text style={styles.required}>*</Text>}
                                        </Text>
                                        <TextInput
                                            style={[styles.input, reasonError ? styles.inputError : null]}
                                            placeholder="Enter here.."
                                            placeholderTextColor={colors.darkgray}
                                            multiline={true}
                                            value={reason}
                                            onChangeText={(text) => {
                                                setReason(text.trimStart());
                                                if (reasonError) setReasonError('');
                                            }}
                                        />
                                        {reasonError ? <Text style={styles.errorText}>{reasonError}</Text> : null}
                                    </>
                                )}
                            </View>

                            {/* Date text UI */}
                            {showDateConfirmation && (
                                <View style={{ paddingHorizontal: Spacing.SCALE_16 }}>
                                    <Text style={styles.dateHeaderText}>Date</Text>
                                    <TouchableOpacity
                                        style={styles.dateTextWrapper}
                                        onPress={() => setShowCalendar(true)}>
                                        <Text>{handleTimeFormat(moment(date).format('YYYY-MM-DD'))}</Text>
                                        <DatePickerSvgIcon height={18} width={18} />
                                    </TouchableOpacity>
                                </View>
                            )}

                            <View style={styles.btnWrapper}>
                                <TealButtonNew
                                    text={
                                        showDateConfirmation
                                            ? 'Cancel'
                                            : showNeedToLogisticPopup || !acceptRequest
                                            ? 'Dismiss'
                                            : 'No'
                                    }
                                    btnStyle={[styles.btnStyle, { backgroundColor: colors.lightgray }]}
                                    textStyle={styles.dismissTextStyle}
                                    onPress={() => {
                                        if (showNeedToLogisticPopup || !acceptRequest || showDateConfirmation) {
                                            navigation.goBack();
                                        } else {
                                            setShowNeedToLogisticPopup(true);
                                        }
                                    }}
                                    disabled={false}
                                    loading={false}
                                />
                                <TealButtonNew
                                    text={
                                        type === 'received-open' && showReason
                                            ? 'Decline Request'
                                            : (type === 'requested-open' ||
                                                  type === 'requested-accepted' ||
                                                  type === 'received-accepted') &&
                                              showReason
                                            ? 'Cancel Request'
                                            : showNeedToLogisticPopup
                                            ? 'Chat Again'
                                            : acceptRequest
                                            ? showDateConfirmation
                                                ? 'Confirm'
                                                : 'Yes'
                                            : declineRequest
                                            ? 'Decline Request'
                                            : 'Chat'
                                    }
                                    btnStyle={[
                                        styles.btnStyle,
                                        {
                                            backgroundColor:
                                                declineRequest || deleteRequest ? colors.orange : colors.tealRgb,
                                        },
                                    ]}
                                    textStyle={styles.unmuteTextStyle}
                                    onPress={() => {
                                        if (showDateConfirmation) {
                                            createGame();
                                        } else if (type.includes('received') && showReason) {
                                            onDeclineRequest();
                                        } else if (type.includes('requested') && showReason) {
                                            handleDeleteRequest(reason);
                                        } else if (showNeedToLogisticPopup || !acceptRequest) {
                                            navigation.goBack();
                                            chatAgainCallBack();
                                        } else {
                                            // setShowDateConfirmation(true);
                                            // navigation.goBack();
                                            setTimeout(() => {
                                                // handleYesButton();
                                                setShowCalendar(true);
                                            }, 100);
                                        }
                                    }}
                                    disabled={false}
                                    loading={false}
                                />
                            </View>
                        </View>
                    </Pressable>
                </KeyboardAvoidingView>
                {showCalendar && (
                    <AcceptRequestModalNew
                        modal={showCalendar}
                        setModal={(modal: any | null) => setShowCalendar(modal)}
                        handleCreateRequest={(date: string) => {
                            if (date) {
                                setDate(date);
                                setShowDateConfirmation(true);
                                setShowCalendar(false);
                            } else {
                                showToast({ type: ERROR, message: 'Date is required' });
                            }
                        }}
                    />
                )}
            </>
        );
};

export default RequestConfirmScreen;

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.whiteRGB,
        borderTopLeftRadius: Size.SIZE_8,
        borderTopRightRadius: Size.SIZE_8,
        width: '100%',
        position: 'absolute',
        bottom: 0,
        paddingVertical: Spacing.SCALE_24,
    },
    centerElements: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: Spacing.SCALE_10,
    },
    iconWrapper: {
        width: Size.SIZE_70,
        height: Size.SIZE_70,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.greyVariant1,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: Spacing.SCALE_16,
    },
    header: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_28,
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '500',
        marginBottom: Spacing.SCALE_12,
    },
    body: {
        color: colors.systemMessageText,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        textAlign: 'center',
        marginBottom: Spacing.SCALE_10,
    },
    declineBody: {
        color: colors.orange,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_24,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        textAlign: 'center',
        marginBottom: Spacing.SCALE_12,
    },
    bodyTextWrapper: {
        width: '100%',
        paddingHorizontal: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_24,
    },
    checkBoxWrapper: {
        alignSelf: 'center',
        height: Size.SIZE_200,
    },
    checkBoxStyle: {
        height: Size.SIZE_16,
        width: Size.SIZE_16,
        borderColor: colors.tealRgb,
        borderRadius: 2,
        borderWidth: 1,
    },
    btnWrapper: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_20,
        backgroundColor: colors.whiteRGB,
        columnGap: Spacing.SCALE_14,
    },
    btnStyle: {
        borderRadius: Size.SIZE_8,
        alignSelf: 'center',
        width: '45%',
        height: Size.SIZE_50,
    },
    dismissTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Medium',
    },
    unmuteTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.whiteRGB,
        fontFamily: 'Ubuntu-Medium',
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_18,
    },
    rowContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    itemTitleStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
        marginLeft: Spacing.SCALE_8,
        width: Size.SIZE_250,
    },
    divider: {
        backgroundColor: colors.greyRgba,
        height: 1,
        marginTop: Spacing.SCALE_12,
        width: '100%',
        alignSelf: 'center',
    },
    headerIconWrapper: {
        width: Size.SIZE_72,
        height: Size.SIZE_72,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.requestedBtnColor,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: Spacing.SCALE_10,
    },
    headerText: {
        fontSize: Typography.FONT_SIZE_24,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_32,
    },
    input: {
        borderWidth: 1,
        borderColor: colors.borderGray,
        borderRadius: Size.SIZE_8,
        paddingHorizontal: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        width: Size.SIZE_340,
        height: Size.SIZE_80,
        textAlignVertical: 'top',
        marginBottom: Spacing.SCALE_24,
    },
    reasonText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        color: colors.systemMessageText,
        alignSelf: 'flex-start',
        marginBottom: Spacing.SCALE_6,
    },
    required: {
        color: colors.orange,
    },
    inputError: {
        borderColor: colors.orange,
    },
    errorText: {
        color: colors.orange,
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        marginTop: -Spacing.SCALE_20,
        marginBottom: Spacing.SCALE_16,
        alignSelf: 'flex-start',
        marginLeft: Spacing.SCALE_4,
    },
    dateTextWrapper: {
        flexDirection: 'row',
        marginBottom: Spacing.SCALE_40,
        borderBottomWidth: 1,
        borderColor: colors.silver,
        paddingBottom: Spacing.SCALE_8,
        justifyContent: 'space-between',
    },
    dateHeaderText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkgray,
        marginBottom: Spacing.SCALE_10,
    },
    dateText: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
        fontWeight: '400',
    },
});
