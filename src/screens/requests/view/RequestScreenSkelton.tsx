import { StyleSheet, View } from 'react-native';
import React from 'react';
import { FlatList } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';

import { Size, Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';

const RequestScreenSkelton = ({ screen = '' }) => {
    const renderItem = () => {
        return (
            <>
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={{
                        width: '50%',
                        height: Size.SIZE_18,
                        marginBottom: Spacing.SCALE_4,
                        borderRadius: 8,
                        marginTop: 10,
                    }}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={{ width: '50%', height: Size.SIZE_18, marginBottom: Spacing.SCALE_10, borderRadius: 8 }}
                />
                <FlatList
                    data={[1, 2]}
                    renderItem={({ item }) => (
                        <ShimmerPlaceholder
                            LinearGradient={LinearGradient}
                            style={{
                                width: '100%',
                                height: Size.SIZE_120,
                                marginBottom: Spacing.SCALE_10,
                                borderRadius: 8,
                            }}
                        />
                    )}
                    showsVerticalScrollIndicator={false}
                    scrollEnabled={false}
                />
            </>
        );
    };
    const RequestDetailSkelton = () => {
        return (
            <FlatList
                data={[1, 2, 3]}
                scrollEnabled={false}
                renderItem={() => {
                    return (
                        <>
                            <ShimmerPlaceholder
                                LinearGradient={LinearGradient}
                                style={{
                                    width: '50%',
                                    height: Size.SIZE_18,
                                    marginBottom: Spacing.SCALE_4,
                                    borderRadius: 8,
                                    marginTop: 10,
                                }}
                            />
                            <ShimmerPlaceholder
                                LinearGradient={LinearGradient}
                                style={{
                                    width: '100%',
                                    height: Size.SIZE_200,
                                    marginBottom: Spacing.SCALE_4,
                                    borderRadius: 8,
                                    marginTop: 10,
                                }}
                            />
                        </>
                    );
                }}
            />
        );
    };
    const RequestHistorySkelton = () => {
        return [1, 2, 3, 4].map(() => {
            return (
                <View style={styles.historySimmerContainer}>
                    <View style={styles.historySimmerRow}>
                        <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.historySimmerRowText} />
                        <ShimmerPlaceholder
                            LinearGradient={LinearGradient}
                            style={[styles.historySimmerRowText, { width: 24, height: Spacing.SCALE_30 }]}
                        />
                    </View>
                    <View style={styles.historySimmerRow}>
                        <ShimmerPlaceholder
                            LinearGradient={LinearGradient}
                            style={[styles.lastWeekTextStyle, { width: '50%' }]}
                        />
                        <ShimmerPlaceholder
                            LinearGradient={LinearGradient}
                            style={[styles.lastWeekTextStyle, { width: '10%', height: 15 }]}
                        />
                    </View>
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={[styles.lastWeekTextStyle, { height: 16 }]}
                    />
                    <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.lastWeekTextStyle} />
                </View>
            );
        });
    };
    const CommonSkelton = () => {
        return (
            <FlatList
                data={[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}
                renderItem={() => (
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={{
                            width: '100%',
                            height: Size.SIZE_80,
                            marginBottom: Spacing.SCALE_10,
                            borderRadius: 8,
                        }}
                    />
                )}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
            />
        );
    };

    const OfferDetailSkelton = () => {
        return (
            <View>
                <View style={{ flexDirection: 'row', alignItems: 'center', columnGap: 12 }}>
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={[styles.lastWeekTextStyle, { width: 40, height: 40, borderRadius: 40 }]}
                    />
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={[styles.lastWeekTextStyle, { width: 112, height: 40, borderRadius: 10 }]}
                    />
                </View>
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { width: '100%', height: 30 }]}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { width: '100%', height: 30, marginTop: 8 }]}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { width: '100%', height: 30, marginTop: 8 }]}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { width: '100%', height: 120, marginTop: 8 }]}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { width: '100%', height: 120, marginTop: 8 }]}
                />
            </View>
        );
    };

    const GolferProfileSkelton = () => {
        return (
            <View style={{ marginTop: 33 }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'flex-end',
                        columnGap: 12,
                        justifyContent: 'space-between',
                    }}>
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={{ width: 92, height: 92, borderRadius: 46 }}
                    />
                    <ShimmerPlaceholder
                        LinearGradient={LinearGradient}
                        style={[styles.lastWeekTextStyle, { width: 200, height: 22 }]}
                    />
                </View>
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { height: 200 }]}
                />
                <ShimmerPlaceholder LinearGradient={LinearGradient} style={styles.lastWeekTextStyle} />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { height: 200 }]}
                />
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { height: 200 }]}
                />
            </View>
        );
    };

    const ReactionUsersListSkelton = () => {
        return (
            <View>
                <ShimmerPlaceholder
                    LinearGradient={LinearGradient}
                    style={[styles.lastWeekTextStyle, { width: 100, height: 22 }]}
                />
                {[1, 2, 3].map((item, index) => {
                    return (
                        <ShimmerPlaceholder
                            key={index}
                            LinearGradient={LinearGradient}
                            style={[styles.lastWeekTextStyle, { width: '100%', height: 22 }]}
                        />
                    );
                })}
            </View>
        );
    };
    return (
        <View
            style={[
                styles.container,
                { paddingHorizontal: screen === 'History' || screen === 'RequestDetail' ? 0 : Spacing.SCALE_16 },
            ]}>
            {screen === 'RequestDetail' ? (
                <RequestDetailSkelton />
            ) : screen === 'golferProfile' ? (
                <GolferProfileSkelton />
            ) : screen === 'History' ? (
                <RequestHistorySkelton />
            ) : screen === 'Common' ? (
                <CommonSkelton />
            ) : screen === 'OfferDetail' ? (
                <OfferDetailSkelton />
            ) : screen === 'ReactionUsersList' ? (
                <ReactionUsersListSkelton />
            ) : (
                <>
                    {renderItem()}
                    {renderItem()}
                </>
            )}
        </View>
    );
};

export default RequestScreenSkelton;

const styles = StyleSheet.create({
    cardStyle: {
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_16,
        width: '100%',
        height: Size.SIZE_190,
        borderRadius: Size.SIZE_16,
    },
    container: {
        flex: 1,
        paddingHorizontal: Spacing.SCALE_16,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    historySimmerContainer: {
        backgroundColor: colors.whiteRGB,
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_16,
        borderRadius: Spacing.SCALE_16,
        marginBottom: Spacing.SCALE_8,
    },
    historySimmerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    historySimmerRowText: {
        width: 62,
        height: 18,
        paddingHorizontal: 0,
        borderRadius: 10,
    },
    lastWeekTextStyle: {
        height: 50,
        width: '100%',
        paddingHorizontal: 0,
        borderRadius: 10,
        marginTop: Spacing.SCALE_14,
    },
});
