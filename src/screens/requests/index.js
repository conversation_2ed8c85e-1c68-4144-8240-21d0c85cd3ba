import React, { useContext, useState, useEffect, useLayoutEffect } from 'react';
import { Text, View } from 'react-native';

import { AuthContext } from '../../context/AuthContext';
import RequestNewScreen from './RequestNewScreen';
import { GlobalContext } from '../../context/contextApi';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { fetcher } from '../../service/fetcher';
import { tokenCheckURL } from '../../service/EndPoint';
import RecommendedClubModal from './RecommendedClubModal';
import { getRecommendedClubs } from './Utils';
import routes from '../../config/routes';
import constants from '../../utils/constants/constants';
import { StreamChatContext } from '../../context/StreamChatContext';
import { REQUEST_CHAT_GROUP } from '../my-TG-Stream-Chat/client';
import BottomSheetComponent from '../../navigation/BottomTabBottomSheet';
import HomeScreenCommonHeader from '../../components/homeScreenComponent/HomeScreenCommonHeader';
const CleverTap = require('clevertap-react-native');

const Requests = () => {
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const { client } = useContext(StreamChatContext);

    const [showRecommendationPopup, setShowRecommendationPopup] = useState(false);
    const [requestedClub, setRequestedClub] = useState({});
    const [recommendedClubs, setRecommendedClubs] = useState([]);
    const [isRecommendedClubRequest, setIsRecommendedClubRequest] = useState(false);
    const [streamUnreadChannel, setStreamUnreadChannel] = useState([]);

    const navigation = useNavigation();
    const isFocused = useIsFocused();

    // It is used to empty when render the page
    useLayoutEffect(() => {
        setStreamUnreadChannel([]);
    }, []);

    useEffect(() => {
        if (state?.currentTab === 1) {
            // connectSendBird();
            // getStreamRequestChannel();
        }
    }, [navigation, state?.currentTab, isFocused]);

    useEffect(() => {
        const messageNewEventListener = client.on('message.new', async (event) => {
            // getStreamRequestChannel();
        });

        return () => {
            messageNewEventListener?.unsubscribe();
        };
    }, [client]);

    //Get stream unread messages
    const getStreamRequestChannel = async () => {
        let page = 0;
        while (1) {
            const channels = await client?.queryChannels(
                {
                    type: REQUEST_CHAT_GROUP,
                    members: { $in: [user?.id] },
                    has_unread: true,
                },
                {},
                { limit: 30, offset: 30 * page },
            );
            page = page + 1;
            if (!channels?.length) {
                break;
            } else {
                let requestIds = channels.map((data) => data?.data?.request_id);
                const updatedUnreadChannels = Array.from(new Set([...requestIds]));
                setStreamUnreadChannel(updatedUnreadChannels);
            }
        }
    };

    useEffect(() => {
        // actions.setUnreadChannels([...streamUnreadChannel]);
    }, [streamUnreadChannel]);

    const matchTokenActive = (arg) => {
        CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.REQUESTS_CREATE_REQUEST);
        fetcher({
            endpoint: tokenCheckURL,
            method: 'POST',
            body: {
                user_id: user?.id,
            },
        }).then((res) => {
            if (res?.canCreate)
                if (arg?.isRecommendedClubRequest) {
                    setIsRecommendedClubRequest(false);
                    navigation.navigate('Create Request', {
                        club: { clubs: arg?.club },
                    });
                } else {
                    navigation.navigate('Create Request', {
                        onGoBack: (data) => {
                            setRequestedClub(data);
                        },
                    });
                }
            else {
                alert(res?.message);
            }
        });
    };

    useEffect(() => {
        if (requestedClub?.id) {
            const getClub = async () => {
                const res = await getRecommendedClubs({ userId: user?.id, clubId: requestedClub?.id });
                if (res?.status === 1) {
                    if (res?.data?.length) {
                        setRecommendedClubs(res?.data);
                        navigation.navigate(routes.RECOMMENDED_CLUB, {
                            recommendedClubs: res?.data,
                            requestedClub,
                        });
                    }
                }
            };

            getClub();
        }
    }, [requestedClub]);

    return (
        <>
            <View style={{ flex: 1 }}>
                <HomeScreenCommonHeader
                    title="Requests"
                    disableSearch={true}
                    showCreateRequestIcon={true}
                    showNotification={false}
                    handleCreateRequest={matchTokenActive}
                />

                <RequestNewScreen />
            </View>
            {showRecommendationPopup && (
                <RecommendedClubModal
                    popupState={[showRecommendationPopup, setShowRecommendationPopup]}
                    requestedClub={requestedClub}
                    setRequestedClub={setRequestedClub}
                    recommendedClubs={recommendedClubs}
                    handleCreateRequest={matchTokenActive}
                    setIsRecommendedClubRequest={setIsRecommendedClubRequest}
                />
            )}
        </>
    );
};

export default Requests;
