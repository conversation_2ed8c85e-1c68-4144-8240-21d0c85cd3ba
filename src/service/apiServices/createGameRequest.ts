import showToast from '../../components/toast/CustomToast';
import { ACCEPT_REQUEST_AS_HOST } from '../EndPoint';
import { fetcher } from '../fetcher';

interface AcceptRequestParams {
    userId: string;
    gameDate: string;
    requestId: string;
}

export const createGameRequest = async (acceptRequestParams: AcceptRequestParams) => {
    return fetcher({
        endpoint: ACCEPT_REQUEST_AS_HOST,
        method: 'POST',
        body: acceptRequestParams,
    })
        .then(async (res) => res)
        .catch(() => {
            showToast({});
        });
}
