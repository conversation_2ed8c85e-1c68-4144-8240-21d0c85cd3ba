import { checkCanCreateRequest } from './checkCanCreateRequest';
import { deleteOffer } from './deleteOffer';
import { getOfferDetails } from './offerDetails';
import { getGolferProfilePhoto } from './getGolferProfilePhoto';
import { withDrawFriendRequest } from './withDrawFriendRequest';
import { togglePegboardVisibility } from './togglePegboardVisibility';
import { getGolfersGameReview } from './getGolfersGameReview';
import { getTealDotStatus } from './tealAndMaintenenceStatus';
import { getPlayingCardNotifications } from './getPlayingCardNotifications';
import { readNotifications } from './readNotification';
import { adminLogin } from './adminLogin';
import { createGameRequest } from './createGameRequest';

export const apiServices = {
    checkCanCreateRequest,
    getOfferDetails,
    deleteOffer,
    getGolferProfilePhoto,
    withDrawFriendRequest,
    togglePegboardVisibility,
    getGolfersGameReview,
    getTealDotStatus,
    getPlayingCardNotifications,
    readNotifications,
    adminLogin,
    createGameRequest,
};
