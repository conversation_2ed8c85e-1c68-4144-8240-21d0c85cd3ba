import { VERIFY_OTP } from '../EndPoint';
import { fetcher } from '../fetcher';

interface AdminVerifyOtpBody {
    otp: string;
    rememberDevice: boolean;
    email: string;
    deviceId: string;
}

// Check user can create request or not
export const adminVerifyOtp = async (body: AdminVerifyOtpBody) => {
    return await fetcher({
        endpoint: VERIFY_OTP,
        method: 'POST',
        body: body,
    });
};
