<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Thousand Greens</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>2.4.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleIdentifier</key>
			<string></string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bundle Id</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.thousand-greens</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bundle Id</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>thousandgreens.page.link</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>133</string>
	<key>CleverTapAccountID</key>
	<string>67Z-WK9-RR7Z</string>
	<key>CleverTapToken</key>
	<string>c30-1a6</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string>CompleteUnlessOpen</string>
	<key>LSMinimumSystemVersion</key>
	<string>12.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>App need to access plugin which use this feature internally.</string>
	<key>NSCameraUsageDescription</key>
	<string>Thousand Greens would like to access your Camera in order to upload a profile picture</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>Thousand Greens would like to access your files in order to save files.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>ThousandGreens requires location services in order to show your location relative to different golf courses.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>ThousandGreens requires location services in order to show your location relative to different golf courses. </string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app would like to save images to your device.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Thousand Greens would like to access your Camera Roll in order to upload a profile picture</string>
	<key>NSPhotoLibraryAllowFallback</key>
	<true/>
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	<key>Photo</key>
	<string></string>
	<key>UIAppFonts</key>
	<array>
		<string>RobotoSlab-Black.ttf</string>
		<string>RobotoSlab-Bold.ttf</string>
		<string>RobotoSlab-ExtraBold.ttf</string>
		<string>RobotoSlab-ExtraLight.ttf</string>
		<string>RobotoSlab-Light.ttf</string>
		<string>RobotoSlab-Medium.ttf</string>
		<string>RobotoSlab-Regular.ttf</string>
		<string>RobotoSlab-SemiBold.ttf</string>
		<string>RobotoSlab-Thin.ttf</string>
		<string>Ubuntu-Bold.ttf</string>
		<string>Ubuntu-BoldItalic.ttf</string>
		<string>Ubuntu-Italic.ttf</string>
		<string>Ubuntu-Light.ttf</string>
		<string>Ubuntu-LightItalic.ttf</string>
		<string>Ubuntu-Medium.ttf</string>
		<string>Ubuntu-MediumItalic.ttf</string>
		<string>Ubuntu-Regular.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
